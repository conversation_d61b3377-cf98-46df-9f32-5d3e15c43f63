import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import Chat from "@/views/AiChat.vue";
import PPTGnerate from "@/views/PPTGnerate.vue";
import PPTCreate from '@/views/PPTCreate.vue';
import LoginVue from '@/views/Login.vue'
import LayoutVue from '@/views/Layout.vue'
import ADPage from '@/components/home/<USER>';
import ArticleCategoryVue from '@/views/article/ArticleCategory.vue'
import ArticleManageVue from '@/views/article/ArticleManage.vue'
import UserAvatarVue from '@/views/user/UserAvatar.vue'
import UserInfoVue from '@/views/user/UserInfo.vue'
import UserResetPasswordVue from '@/views/user/UserResetPassword.vue'
import LessonPlanGenerator from '@/views/LessonPlanGenerator.vue'
const routes = [
  {
    path: '/',
    name: 'homepage',
    component: HomeView
  },

  { path: '/login', component: LoginVue },
        {
        path: '/', component: LayoutVue,redirect:'/article/manage', children: [
            { path: '/article/category', component: ArticleCategoryVue },
            { path: '/article/manage', component: ArticleManageVue },
            { path: '/lesson/generator', component: LessonPlanGenerator },
            { path: '/jiaoan', component: LessonPlanGenerator },
            { path: '/user/info', component: UserInfoVue },
            { path: '/user/avatar', component: UserAvatarVue },
            { path: '/user/resetPassword', component: UserResetPasswordVue }
        ]
    },
   {
        path: '/aichat',
        name: 'aichat',
        component: Chat
    },
    {
        path: '/pptgenerate',
        name: 'pptgenerate',
        component: PPTGnerate
    }

  ,



    {
        path: '/pptcreate',
        name: 'pptcreate',
        component: PPTCreate
    },
    {
        path: '/jiaoan',
        name: 'jiaoan',
        component: LessonPlanGenerator
    },
    {   path: '/Home', component: ADPage}

]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

export default router
