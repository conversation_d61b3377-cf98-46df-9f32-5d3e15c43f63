<script>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Download, Plus, Delete, Edit, Loading } from '@element-plus/icons-vue'
import { generateLessonPlanService } from '@/api/lessonPlan.js'
// import { saveLessonPlanService } from '@/api/lessonPlan.js' // 暂时注释，将来可能会用到
import LessonPlanPreview from '@/components/LessonPlanPreview.vue'

export default {
  name: 'LessonPlanGenerator',
  components: {
    LessonPlanPreview
  },
  setup() {
    // 表单数据
    const formData = reactive({
      subject: '', // 学科
      grade: '', // 年级
      topic: '', // 课题
      duration: 45, // 课时长度（分钟）
      objectives: [], // 教学目标
      keyPoints: '', // 重点难点
      materials: '', // 教学材料
      methods: [], // 教学方法
      steps: [], // 教学步骤
      homework: '', // 作业布置
      reflection: '' // 教学反思
    })

    // 教学目标列表
    const objectiveInput = ref('')

    // 教学方法选项
    const methodOptions = [
      { label: '讲授法', value: 'lecture' },
      { label: '讨论法', value: 'discussion' },
      { label: '实验法', value: 'experiment' },
      { label: '演示法', value: 'demonstration' },
      { label: '练习法', value: 'practice' },
      { label: '案例分析法', value: 'case_study' },
      { label: '小组合作', value: 'group_work' },
      { label: '多媒体教学', value: 'multimedia' }
    ]

    // 教学步骤
    const stepInput = reactive({
      title: '',
      content: '',
      time: 5
    })

    // 学科选项
    const subjectOptions = [
      '语文', '数学', '英语', '物理', '化学', '生物',
      '历史', '地理', '政治', '音乐', '美术', '体育', '信息技术'
    ]

    // 年级选项
    const gradeOptions = [
      '一年级', '二年级', '三年级', '四年级', '五年级', '六年级',
      '七年级', '八年级', '九年级', '高一', '高二', '高三'
    ]

    // 添加教学目标
    const addObjective = () => {
      if (objectiveInput.value.trim()) {
        formData.objectives.push(objectiveInput.value.trim())
        objectiveInput.value = ''
      }
    }

    // 删除教学目标
    const removeObjective = (index) => {
      formData.objectives.splice(index, 1)
    }

    // 添加教学步骤
    const addStep = () => {
      if (stepInput.title.trim() && stepInput.content.trim()) {
        formData.steps.push({
          title: stepInput.title,
          content: stepInput.content,
          time: stepInput.time
        })
        stepInput.title = ''
        stepInput.content = ''
        stepInput.time = 5
      }
    }

    // 删除教学步骤
    const removeStep = (index) => {
      formData.steps.splice(index, 1)
    }

    // 生成状态
    const isGenerating = ref(false)

    // 预览状态
    const showPreview = ref(false)

    // 生成教案
    const generateLessonPlan = async () => {
      // 验证必填字段
      if (!formData.subject || !formData.grade || !formData.topic) {
        ElMessage.error('请填写学科、年级和课题')
        return
      }

      if (formData.objectives.length === 0) {
        ElMessage.error('请至少添加一个教学目标')
        return
      }

      if (formData.steps.length === 0) {
        ElMessage.error('请至少添加一个教学步骤')
        return
      }

      try {
        isGenerating.value = true
        ElMessage.info('教案生成中，请稍候...')

        // 调用后端API生成教案
        const result = await generateLessonPlanService(formData)

        if (result.code === 0) {
          ElMessage.success('教案生成成功！')
          // 可以在这里处理生成的教案数据
          console.log('生成的教案:', result.data)
        } else {
          ElMessage.error(result.message || '教案生成失败')
        }

      } catch (error) {
        console.error('教案生成错误:', error)
        ElMessage.error('教案生成失败：' + (error.message || '网络错误'))
      } finally {
        isGenerating.value = false
      }
    }

    // 重置表单
    const resetForm = () => {
      ElMessageBox.confirm('确认重置所有内容吗？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        Object.assign(formData, {
          subject: '',
          grade: '',
          topic: '',
          duration: 45,
          objectives: [],
          keyPoints: '',
          materials: '',
          methods: [],
          steps: [],
          homework: '',
          reflection: ''
        })
        objectiveInput.value = ''
        ElMessage.success('表单已重置')
      })
    }

    // 预览教案
    const previewLessonPlan = () => {
      // 验证基本信息
      if (!formData.subject || !formData.grade || !formData.topic) {
        ElMessage.error('请先填写基本信息（学科、年级、课题）')
        return
      }

      showPreview.value = true
    }

    // 导出教案
    const handleExport = (format) => {
      ElMessage.info(`正在导出${format.toUpperCase()}格式...`)
      // 这里可以调用导出API
    }

    // 打印教案
    const handlePrint = () => {
      window.print()
    }

    return {
      formData,
      objectiveInput,
      methodOptions,
      stepInput,
      subjectOptions,
      gradeOptions,
      isGenerating,
      showPreview,
      addObjective,
      removeObjective,
      addStep,
      removeStep,
      generateLessonPlan,
      resetForm,
      previewLessonPlan,
      handleExport,
      handlePrint,
      // 图标
      Document,
      Download,
      Plus,
      Delete,
      Edit,
      Loading
    }
  }
}
</script>

<template>
  <el-card class="lesson-plan-container">
    <template #header>
      <div class="header">
        <span class="title">
          <el-icon><Document /></el-icon>
          智能教案生成器
        </span>
        <div class="actions">
          <el-button type="info" @click="previewLessonPlan">
            <el-icon><Edit /></el-icon>
            预览
          </el-button>
          <el-button type="warning" @click="resetForm">
            重置
          </el-button>
          <el-button
            type="primary"
            @click="generateLessonPlan"
            :loading="isGenerating"
            :disabled="isGenerating"
          >
            <el-icon v-if="!isGenerating"><Download /></el-icon>
            <el-icon v-else><Loading /></el-icon>
            {{ isGenerating ? '生成中...' : '生成教案' }}
          </el-button>
        </div>
      </div>
    </template>

    <el-form :model="formData" label-width="120px" class="lesson-form">
      <!-- 基本信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <h3>基本信息</h3>
        </template>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="学科" required>
              <el-select v-model="formData.subject" placeholder="请选择学科" style="width: 100%">
                <el-option
                  v-for="subject in subjectOptions"
                  :key="subject"
                  :label="subject"
                  :value="subject"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="年级" required>
              <el-select v-model="formData.grade" placeholder="请选择年级" style="width: 100%">
                <el-option
                  v-for="grade in gradeOptions"
                  :key="grade"
                  :label="grade"
                  :value="grade"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="课时长度">
              <el-input-number
                v-model="formData.duration"
                :min="10"
                :max="120"
                :step="5"
                style="width: 100%"
              />
              <span style="margin-left: 8px;">分钟</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="课题" required>
          <el-input
            v-model="formData.topic"
            placeholder="请输入课题名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-card>

      <!-- 教学目标 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <h3>教学目标</h3>
        </template>

        <div class="objective-input">
          <el-input
            v-model="objectiveInput"
            placeholder="请输入教学目标"
            @keyup.enter="addObjective"
          >
            <template #append>
              <el-button @click="addObjective" :icon="Plus">添加</el-button>
            </template>
          </el-input>
        </div>

        <div class="objectives-list" v-if="formData.objectives.length > 0">
          <el-tag
            v-for="(objective, index) in formData.objectives"
            :key="index"
            closable
            @close="removeObjective(index)"
            class="objective-tag"
          >
            {{ objective }}
          </el-tag>
        </div>
      </el-card>

      <!-- 重点难点 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <h3>重点难点</h3>
        </template>

        <el-form-item label="教学重点">
          <el-input
            v-model="formData.keyPoints"
            type="textarea"
            :rows="3"
            placeholder="请描述本课的教学重点和难点"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-card>

      <!-- 教学方法 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <h3>教学方法</h3>
        </template>

        <el-form-item label="教学方法">
          <el-checkbox-group v-model="formData.methods">
            <el-checkbox
              v-for="method in methodOptions"
              :key="method.value"
              :label="method.value"
            >
              {{ method.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="教学材料">
          <el-input
            v-model="formData.materials"
            type="textarea"
            :rows="2"
            placeholder="请列出所需的教学材料和设备"
            maxlength="300"
            show-word-limit
          />
        </el-form-item>
      </el-card>

      <!-- 教学步骤 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <h3>教学步骤</h3>
        </template>

        <div class="step-input">
          <el-row :gutter="10">
            <el-col :span="6">
              <el-input
                v-model="stepInput.title"
                placeholder="步骤标题"
                maxlength="50"
              />
            </el-col>
            <el-col :span="12">
              <el-input
                v-model="stepInput.content"
                placeholder="步骤内容"
                maxlength="200"
              />
            </el-col>
            <el-col :span="4">
              <el-input-number
                v-model="stepInput.time"
                :min="1"
                :max="60"
                placeholder="时长"
                style="width: 100%"
              />
            </el-col>
            <el-col :span="2">
              <el-button @click="addStep" :icon="Plus" type="primary">添加</el-button>
            </el-col>
          </el-row>
        </div>

        <div class="steps-list" v-if="formData.steps.length > 0">
          <el-timeline>
            <el-timeline-item
              v-for="(step, index) in formData.steps"
              :key="index"
              :timestamp="`${step.time}分钟`"
            >
              <el-card class="step-card">
                <div class="step-header">
                  <h4>{{ step.title }}</h4>
                  <el-button
                    @click="removeStep(index)"
                    :icon="Delete"
                    size="small"
                    type="danger"
                    text
                  />
                </div>
                <p>{{ step.content }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-card>

      <!-- 作业与反思 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <h3>作业与反思</h3>
        </template>

        <el-form-item label="作业布置">
          <el-input
            v-model="formData.homework"
            type="textarea"
            :rows="3"
            placeholder="请描述课后作业安排"
            maxlength="300"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="教学反思">
          <el-input
            v-model="formData.reflection"
            type="textarea"
            :rows="3"
            placeholder="请填写教学反思（可在课后补充）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-card>
    </el-form>

    <!-- 教案预览组件 -->
    <LessonPlanPreview
      v-model:visible="showPreview"
      :lesson-plan="formData"
      @export="handleExport"
      @print="handlePrint"
    />
  </el-card>
</template>

<style lang="scss" scoped>
.lesson-plan-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .title {
      font-size: 24px;
      font-weight: bold;
      display: flex;
      align-items: center;
      gap: 8px;
      color: #409eff;
    }

    .actions {
      display: flex;
      gap: 10px;
    }
  }
}

.lesson-form {
  .form-section {
    margin-bottom: 20px;
    border: 1px solid #e4e7ed;

    h3 {
      margin: 0;
      color: #409eff;
      font-size: 16px;
    }
  }

  .objective-input {
    margin-bottom: 15px;
  }

  .objectives-list {
    .objective-tag {
      margin: 5px 5px 5px 0;
      padding: 8px 12px;
      font-size: 14px;
      background-color: #f0f9ff;
      border-color: #409eff;
    }
  }

  .step-input {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
  }

  .steps-list {
    .step-card {
      margin-bottom: 10px;

      .step-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        h4 {
          margin: 0;
          color: #303133;
          font-size: 14px;
        }
      }

      p {
        margin: 0;
        color: #606266;
        line-height: 1.6;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .lesson-plan-container {
    margin: 10px;

    .header {
      flex-direction: column;
      gap: 15px;

      .actions {
        width: 100%;
        justify-content: center;
      }
    }
  }

  .step-input {
    .el-row {
      .el-col {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
