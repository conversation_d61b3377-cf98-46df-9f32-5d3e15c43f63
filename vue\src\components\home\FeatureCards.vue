<script setup>
import { useRouter } from 'vue-router'
import { Document, ChatDotRound, Monitor, BookOpen } from '@element-plus/icons-vue'

const router = useRouter()

// 功能卡片数据
const features = [
  {
    id: 'lesson-generator',
    title: '智能教案生成器',
    description: '基于AI技术的智能教案生成工具，支持多学科、多年级的教案自动生成，包含教学目标、重点难点、教学步骤等完整内容。',
    icon: Document,
    route: '/lesson/generator',
    color: '#409eff',
    bgColor: '#ecf5ff'
  },
  {
    id: 'ai-chat',
    title: 'AI学习助手',
    description: 'AI驱动的智能问答系统，为师生提供实时的学习指导和答疑服务，支持多种学科知识点解析。',
    icon: ChatDotRound,
    route: '/aichat',
    color: '#67c23a',
    bgColor: '#f0f9eb'
  },
  {
    id: 'ppt-generator',
    title: 'PPT课件生成',
    description: '快速生成精美的教学课件，支持多种模板和样式，让教学内容更加生动有趣。',
    icon: Monitor,
    route: '/pptgenerate',
    color: '#e6a23c',
    bgColor: '#fdf6ec'
  },
  {
    id: 'knowledge-base',
    title: '教学资源库',
    description: '丰富的教学资源库，包含各学科的教学素材、习题库、参考资料等，助力教学质量提升。',
    icon: BookOpen,
    route: '/resources',
    color: '#f56c6c',
    bgColor: '#fef0f0'
  }
]

// 跳转到指定功能
const navigateToFeature = (route) => {
  router.push(route)
}
</script>

<template>
  <div class="feature-cards-container">
    <div class="header">
      <h2>师说教学辅助系统</h2>
      <p>智能教学助手，助力教育创新</p>
    </div>
    
    <div class="cards-grid">
      <div 
        v-for="feature in features" 
        :key="feature.id"
        class="feature-card"
        :style="{ '--card-color': feature.color, '--card-bg': feature.bgColor }"
        @click="navigateToFeature(feature.route)"
      >
        <div class="card-icon">
          <el-icon :size="40" :color="feature.color">
            <component :is="feature.icon" />
          </el-icon>
        </div>
        
        <div class="card-content">
          <h3>{{ feature.title }}</h3>
          <p>{{ feature.description }}</p>
        </div>
        
        <div class="card-action">
          <el-button type="primary" :style="{ backgroundColor: feature.color, borderColor: feature.color }">
            立即体验
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.feature-cards-container {
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
  
  .header {
    text-align: center;
    margin-bottom: 50px;
    
    h2 {
      font-size: 32px;
      color: #303133;
      margin-bottom: 10px;
      font-weight: 600;
    }
    
    p {
      font-size: 16px;
      color: #606266;
      margin: 0;
    }
  }
  
  .cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    
    .feature-card {
      background: white;
      border-radius: 12px;
      padding: 30px 25px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-color);
      }
      
      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: var(--card-color);
        
        .card-icon {
          transform: scale(1.1);
        }
      }
      
      .card-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 80px;
        background: var(--card-bg);
        border-radius: 50%;
        margin: 0 auto 20px;
        transition: transform 0.3s ease;
      }
      
      .card-content {
        text-align: center;
        margin-bottom: 25px;
        
        h3 {
          font-size: 20px;
          color: #303133;
          margin-bottom: 15px;
          font-weight: 600;
        }
        
        p {
          font-size: 14px;
          color: #606266;
          line-height: 1.6;
          margin: 0;
        }
      }
      
      .card-action {
        text-align: center;
        
        .el-button {
          width: 100%;
          font-weight: 500;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .feature-cards-container {
    padding: 20px 15px;
    
    .header {
      margin-bottom: 30px;
      
      h2 {
        font-size: 24px;
      }
    }
    
    .cards-grid {
      grid-template-columns: 1fr;
      gap: 20px;
      
      .feature-card {
        padding: 25px 20px;
      }
    }
  }
}
</style>
