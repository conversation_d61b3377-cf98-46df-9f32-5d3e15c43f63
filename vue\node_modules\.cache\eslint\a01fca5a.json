[{"C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\main.js": "1", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\App.vue": "2", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\router\\index.js": "3", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\store\\index.js": "4", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\HomeView.vue": "5", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\PPTCreate.vue": "6", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\PPTGnerate.vue": "7", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\Login.vue": "8", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\user\\UserAvatar.vue": "9", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\user\\UserResetPassword.vue": "10", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\Layout.vue": "11", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\AiChat.vue": "12", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\home.vue": "13", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\user\\UserInfo.vue": "14", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\article\\ArticleCategory.vue": "15", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\article\\ArticleManage.vue": "16", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\AdvancedPPTCreate.vue": "17", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\SimplePPTCreate.vue": "18", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\api\\user.js": "19", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\stores\\token.js": "20", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\homeRight.vue": "21", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\homelogo.vue": "22", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\stores\\userInfo.js": "23", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\api\\article.js": "24", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\utils\\request.js": "25", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\WelcomeItem.vue": "26", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconDocumentation.vue": "27", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconEcosystem.vue": "28", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconCommunity.vue": "29", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconTooling.vue": "30", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconSupport.vue": "31", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\LessonPlanGenerator.vue": "32", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\LessonPlanPreview.vue": "33", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\api\\lessonPlan.js": "34"}, {"size": 1102, "mtime": 1747455438885, "results": "35", "hashOfConfig": "36"}, {"size": 275, "mtime": 1744694446924, "results": "37", "hashOfConfig": "36"}, {"size": 1871, "mtime": 1748594931208, "results": "38", "hashOfConfig": "36"}, {"size": 157, "mtime": 1744693766066, "results": "39", "hashOfConfig": "36"}, {"size": 3785, "mtime": 1746159238211, "results": "40", "hashOfConfig": "36"}, {"size": 7609, "mtime": 1746897833300, "results": "41", "hashOfConfig": "36"}, {"size": 18569, "mtime": 1746002635798, "results": "42", "hashOfConfig": "36"}, {"size": 6517, "mtime": 1747456162565, "results": "43", "hashOfConfig": "36"}, {"size": 2996, "mtime": 1746261676363, "results": "44", "hashOfConfig": "36"}, {"size": 41, "mtime": 1697874604322, "results": "45", "hashOfConfig": "36"}, {"size": 7316, "mtime": 1748594968996, "results": "46", "hashOfConfig": "36"}, {"size": 15176, "mtime": 1746336034237, "results": "47", "hashOfConfig": "36"}, {"size": 881, "mtime": 1747454917700, "results": "48", "hashOfConfig": "36"}, {"size": 2198, "mtime": 1698551370977, "results": "49", "hashOfConfig": "36"}, {"size": 6204, "mtime": 1747455361145, "results": "50", "hashOfConfig": "36"}, {"size": 11919, "mtime": 1747455082647, "results": "51", "hashOfConfig": "36"}, {"size": 10842, "mtime": 1746897906221, "results": "52", "hashOfConfig": "36"}, {"size": 10213, "mtime": 1746897903454, "results": "53", "hashOfConfig": "36"}, {"size": 1122, "mtime": 1698562785111, "results": "54", "hashOfConfig": "36"}, {"size": 686, "mtime": 1698375212156, "results": "55", "hashOfConfig": "36"}, {"size": 2045, "mtime": 1744860955970, "results": "56", "hashOfConfig": "36"}, {"size": 1511, "mtime": 1747455021080, "results": "57", "hashOfConfig": "36"}, {"size": 412, "mtime": 1698502375123, "results": "58", "hashOfConfig": "36"}, {"size": 1149, "mtime": 1747455934630, "results": "59", "hashOfConfig": "36"}, {"size": 1023, "mtime": 1747455464284, "results": "60", "hashOfConfig": "36"}, {"size": 1414, "mtime": 1744532892223, "results": "61", "hashOfConfig": "36"}, {"size": 1254, "mtime": 1744532892207, "results": "62", "hashOfConfig": "36"}, {"size": 1977, "mtime": 1744532892210, "results": "63", "hashOfConfig": "36"}, {"size": 1054, "mtime": 1744532892201, "results": "64", "hashOfConfig": "36"}, {"size": 913, "mtime": 1744532892216, "results": "65", "hashOfConfig": "36"}, {"size": 288, "mtime": 1744532892214, "results": "66", "hashOfConfig": "36"}, {"size": 15381, "mtime": 1748595334089, "results": "67", "hashOfConfig": "36"}, {"size": 6788, "mtime": 1748595245016, "results": "68", "hashOfConfig": "36"}, {"size": 1301, "mtime": 1748594989170, "results": "69", "hashOfConfig": "36"}, {"filePath": "70", "messages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1odrflj", {"filePath": "72", "messages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\main.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\App.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\router\\index.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\store\\index.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\HomeView.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\PPTCreate.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\PPTGnerate.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\Login.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\user\\UserAvatar.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\user\\UserResetPassword.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\Layout.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\AiChat.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\home.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\user\\UserInfo.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\article\\ArticleCategory.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\article\\ArticleManage.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\AdvancedPPTCreate.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\SimplePPTCreate.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\api\\user.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\stores\\token.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\homeRight.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\homelogo.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\stores\\userInfo.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\api\\article.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\utils\\request.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\WelcomeItem.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconDocumentation.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconEcosystem.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconCommunity.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconTooling.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconSupport.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\LessonPlanGenerator.vue", ["138"], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\LessonPlanPreview.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\api\\lessonPlan.js", [], {"ruleId": "139", "severity": 2, "message": "140", "line": 5, "column": 37, "nodeType": "141", "messageId": "142", "endLine": 5, "endColumn": 58}, "no-unused-vars", "'saveLessonPlanService' is defined but never used.", "Identifier", "unusedVar"]