[{"C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\main.js": "1", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\App.vue": "2", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\router\\index.js": "3", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\store\\index.js": "4", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\HomeView.vue": "5", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\PPTCreate.vue": "6", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\PPTGnerate.vue": "7", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\Login.vue": "8", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\user\\UserAvatar.vue": "9", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\user\\UserResetPassword.vue": "10", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\Layout.vue": "11", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\AiChat.vue": "12", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\home.vue": "13", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\user\\UserInfo.vue": "14", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\article\\ArticleCategory.vue": "15", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\article\\ArticleManage.vue": "16", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\AdvancedPPTCreate.vue": "17", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\SimplePPTCreate.vue": "18", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\api\\user.js": "19", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\stores\\token.js": "20", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\homeRight.vue": "21", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\homelogo.vue": "22", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\stores\\userInfo.js": "23", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\api\\article.js": "24", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\utils\\request.js": "25", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\WelcomeItem.vue": "26", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconDocumentation.vue": "27", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconEcosystem.vue": "28", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconCommunity.vue": "29", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconTooling.vue": "30", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconSupport.vue": "31", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\LessonPlanGenerator.vue": "32", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\LessonPlanPreview.vue": "33", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\api\\lessonPlan.js": "34", "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\FeatureCards.vue": "35"}, {"size": 1102, "mtime": 1747455438885, "results": "36", "hashOfConfig": "37"}, {"size": 275, "mtime": 1744694446924, "results": "38", "hashOfConfig": "37"}, {"size": 1963, "mtime": 1748597188549, "results": "39", "hashOfConfig": "37"}, {"size": 157, "mtime": 1744693766066, "results": "40", "hashOfConfig": "37"}, {"size": 3785, "mtime": 1748596860329, "results": "41", "hashOfConfig": "37"}, {"size": 7609, "mtime": 1746897833300, "results": "42", "hashOfConfig": "37"}, {"size": 18569, "mtime": 1746002635798, "results": "43", "hashOfConfig": "37"}, {"size": 6517, "mtime": 1747456162565, "results": "44", "hashOfConfig": "37"}, {"size": 2996, "mtime": 1746261676363, "results": "45", "hashOfConfig": "37"}, {"size": 41, "mtime": 1697874604322, "results": "46", "hashOfConfig": "37"}, {"size": 7306, "mtime": 1748597111075, "results": "47", "hashOfConfig": "37"}, {"size": 15176, "mtime": 1746336034237, "results": "48", "hashOfConfig": "37"}, {"size": 381, "mtime": 1748596637255, "results": "49", "hashOfConfig": "37"}, {"size": 2198, "mtime": 1698551370977, "results": "50", "hashOfConfig": "37"}, {"size": 6204, "mtime": 1747455361145, "results": "51", "hashOfConfig": "37"}, {"size": 11919, "mtime": 1747455082647, "results": "52", "hashOfConfig": "37"}, {"size": 10842, "mtime": 1746897906221, "results": "53", "hashOfConfig": "37"}, {"size": 10213, "mtime": 1746897903454, "results": "54", "hashOfConfig": "37"}, {"size": 1122, "mtime": 1698562785111, "results": "55", "hashOfConfig": "37"}, {"size": 686, "mtime": 1698375212156, "results": "56", "hashOfConfig": "37"}, {"size": 2515, "mtime": 1748596551495, "results": "57", "hashOfConfig": "37"}, {"size": 1511, "mtime": 1747455021080, "results": "58", "hashOfConfig": "37"}, {"size": 412, "mtime": 1698502375123, "results": "59", "hashOfConfig": "37"}, {"size": 1149, "mtime": 1747455934630, "results": "60", "hashOfConfig": "37"}, {"size": 1023, "mtime": 1747455464284, "results": "61", "hashOfConfig": "37"}, {"size": 1414, "mtime": 1744532892223, "results": "62", "hashOfConfig": "37"}, {"size": 1254, "mtime": 1744532892207, "results": "63", "hashOfConfig": "37"}, {"size": 1977, "mtime": 1744532892210, "results": "64", "hashOfConfig": "37"}, {"size": 1054, "mtime": 1744532892201, "results": "65", "hashOfConfig": "37"}, {"size": 913, "mtime": 1744532892216, "results": "66", "hashOfConfig": "37"}, {"size": 288, "mtime": 1744532892214, "results": "67", "hashOfConfig": "37"}, {"size": 15461, "mtime": 1748596886868, "results": "68", "hashOfConfig": "37"}, {"size": 6698, "mtime": 1748596086222, "results": "69", "hashOfConfig": "37"}, {"size": 1301, "mtime": 1748594989170, "results": "70", "hashOfConfig": "37"}, {"size": 4836, "mtime": 1748597079606, "results": "71", "hashOfConfig": "37"}, {"filePath": "72", "messages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "74"}, "1odrflj", {"filePath": "75", "messages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "77"}, {"filePath": "78", "messages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "74"}, {"filePath": "82", "messages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "77"}, {"filePath": "86", "messages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "77"}, {"filePath": "88", "messages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "77"}, {"filePath": "90", "messages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "77"}, {"filePath": "92", "messages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "77"}, {"filePath": "94", "messages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "77"}, {"filePath": "98", "messages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "77"}, {"filePath": "102", "messages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "77"}, {"filePath": "104", "messages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "77"}, {"filePath": "106", "messages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "77"}, {"filePath": "108", "messages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "77"}, {"filePath": "110", "messages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "74"}, {"filePath": "112", "messages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "74"}, {"filePath": "114", "messages": "115", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "118"}, {"filePath": "119", "messages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "74"}, {"filePath": "121", "messages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "74"}, {"filePath": "123", "messages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "74"}, {"filePath": "125", "messages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "118"}, {"filePath": "127", "messages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "118"}, {"filePath": "129", "messages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "118"}, {"filePath": "131", "messages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "118"}, {"filePath": "133", "messages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "118"}, {"filePath": "135", "messages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "118"}, {"filePath": "137", "messages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "77"}, {"filePath": "141", "messages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "74"}, {"filePath": "143", "messages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\main.js", [], [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\App.vue", [], [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\router\\index.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\store\\index.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\HomeView.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\PPTCreate.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\PPTGnerate.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\Login.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\user\\UserAvatar.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\user\\UserResetPassword.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\Layout.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\AiChat.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\home.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\user\\UserInfo.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\article\\ArticleCategory.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\article\\ArticleManage.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\AdvancedPPTCreate.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\SimplePPTCreate.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\api\\user.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\stores\\token.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\homeRight.vue", ["145"], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\homelogo.vue", [], [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\stores\\userInfo.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\api\\article.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\utils\\request.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\WelcomeItem.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconDocumentation.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconEcosystem.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconCommunity.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconTooling.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\icons\\IconSupport.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\LessonPlanGenerator.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\LessonPlanPreview.vue", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\api\\lessonPlan.js", [], "C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\FeatureCards.vue", [], {"ruleId": "146", "severity": 2, "message": "147", "line": 23, "column": 7, "nodeType": "148", "messageId": "149", "endLine": 23, "endColumn": 22}, "no-unused-vars", "'goToPPTGenerate' is assigned a value but never used.", "Identifier", "unusedVar"]