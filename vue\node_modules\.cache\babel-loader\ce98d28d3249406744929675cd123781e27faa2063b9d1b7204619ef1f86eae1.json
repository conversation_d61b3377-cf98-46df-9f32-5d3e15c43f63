{"ast": null, "code": "import request from '@/utils/request.js';\n\n// 生成教案\nexport const generateLessonPlanService = lessonPlanData => {\n  return request.post('/lesson-plan/generate', lessonPlanData);\n};\n\n// 保存教案\nexport const saveLessonPlanService = lessonPlanData => {\n  return request.post('/lesson-plan/save', lessonPlanData);\n};\n\n// 获取教案列表\nexport const getLessonPlanListService = params => {\n  return request.get('/lesson-plan/list', {\n    params\n  });\n};\n\n// 获取教案详情\nexport const getLessonPlanDetailService = id => {\n  return request.get(`/lesson-plan/${id}`);\n};\n\n// 更新教案\nexport const updateLessonPlanService = (id, lessonPlanData) => {\n  return request.put(`/lesson-plan/${id}`, lessonPlanData);\n};\n\n// 删除教案\nexport const deleteLessonPlanService = id => {\n  return request.delete(`/lesson-plan/${id}`);\n};\n\n// 导出教案\nexport const exportLessonPlanService = (id, format = 'pdf') => {\n  return request.get(`/lesson-plan/${id}/export`, {\n    params: {\n      format\n    },\n    responseType: 'blob'\n  });\n};\n\n// 获取教案模板\nexport const getLessonPlanTemplatesService = () => {\n  return request.get('/lesson-plan/templates');\n};\n\n// AI智能优化教案\nexport const optimizeLessonPlanService = lessonPlanData => {\n  return request.post('/lesson-plan/optimize', lessonPlanData);\n};", "map": {"version": 3, "names": ["request", "generateLessonPlanService", "lessonPlanData", "post", "saveLessonPlanService", "getLessonPlanListService", "params", "get", "getLessonPlanDetailService", "id", "updateLessonPlanService", "put", "deleteLessonPlanService", "delete", "exportLessonPlanService", "format", "responseType", "getLessonPlanTemplatesService", "optimizeLessonPlanService"], "sources": ["C:/Users/<USER>/Desktop/shishuo/vue/src/api/lessonPlan.js"], "sourcesContent": ["import request from '@/utils/request.js'\n\n// 生成教案\nexport const generateLessonPlanService = (lessonPlanData) => {\n  return request.post('/lesson-plan/generate', lessonPlanData)\n}\n\n// 保存教案\nexport const saveLessonPlanService = (lessonPlanData) => {\n  return request.post('/lesson-plan/save', lessonPlanData)\n}\n\n// 获取教案列表\nexport const getLessonPlanListService = (params) => {\n  return request.get('/lesson-plan/list', { params })\n}\n\n// 获取教案详情\nexport const getLessonPlanDetailService = (id) => {\n  return request.get(`/lesson-plan/${id}`)\n}\n\n// 更新教案\nexport const updateLessonPlanService = (id, lessonPlanData) => {\n  return request.put(`/lesson-plan/${id}`, lessonPlanData)\n}\n\n// 删除教案\nexport const deleteLessonPlanService = (id) => {\n  return request.delete(`/lesson-plan/${id}`)\n}\n\n// 导出教案\nexport const exportLessonPlanService = (id, format = 'pdf') => {\n  return request.get(`/lesson-plan/${id}/export`, {\n    params: { format },\n    responseType: 'blob'\n  })\n}\n\n// 获取教案模板\nexport const getLessonPlanTemplatesService = () => {\n  return request.get('/lesson-plan/templates')\n}\n\n// AI智能优化教案\nexport const optimizeLessonPlanService = (lessonPlanData) => {\n  return request.post('/lesson-plan/optimize', lessonPlanData)\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,oBAAoB;;AAExC;AACA,OAAO,MAAMC,yBAAyB,GAAIC,cAAc,IAAK;EAC3D,OAAOF,OAAO,CAACG,IAAI,CAAC,uBAAuB,EAAED,cAAc,CAAC;AAC9D,CAAC;;AAED;AACA,OAAO,MAAME,qBAAqB,GAAIF,cAAc,IAAK;EACvD,OAAOF,OAAO,CAACG,IAAI,CAAC,mBAAmB,EAAED,cAAc,CAAC;AAC1D,CAAC;;AAED;AACA,OAAO,MAAMG,wBAAwB,GAAIC,MAAM,IAAK;EAClD,OAAON,OAAO,CAACO,GAAG,CAAC,mBAAmB,EAAE;IAAED;EAAO,CAAC,CAAC;AACrD,CAAC;;AAED;AACA,OAAO,MAAME,0BAA0B,GAAIC,EAAE,IAAK;EAChD,OAAOT,OAAO,CAACO,GAAG,CAAC,gBAAgBE,EAAE,EAAE,CAAC;AAC1C,CAAC;;AAED;AACA,OAAO,MAAMC,uBAAuB,GAAGA,CAACD,EAAE,EAAEP,cAAc,KAAK;EAC7D,OAAOF,OAAO,CAACW,GAAG,CAAC,gBAAgBF,EAAE,EAAE,EAAEP,cAAc,CAAC;AAC1D,CAAC;;AAED;AACA,OAAO,MAAMU,uBAAuB,GAAIH,EAAE,IAAK;EAC7C,OAAOT,OAAO,CAACa,MAAM,CAAC,gBAAgBJ,EAAE,EAAE,CAAC;AAC7C,CAAC;;AAED;AACA,OAAO,MAAMK,uBAAuB,GAAGA,CAACL,EAAE,EAAEM,MAAM,GAAG,KAAK,KAAK;EAC7D,OAAOf,OAAO,CAACO,GAAG,CAAC,gBAAgBE,EAAE,SAAS,EAAE;IAC9CH,MAAM,EAAE;MAAES;IAAO,CAAC;IAClBC,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMC,6BAA6B,GAAGA,CAAA,KAAM;EACjD,OAAOjB,OAAO,CAACO,GAAG,CAAC,wBAAwB,CAAC;AAC9C,CAAC;;AAED;AACA,OAAO,MAAMW,yBAAyB,GAAIhB,cAAc,IAAK;EAC3D,OAAOF,OAAO,CAACG,IAAI,CAAC,uBAAuB,EAAED,cAAc,CAAC;AAC9D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}