{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { useRouter } from 'vue-router';\nimport { Document, ChatDotRound, Monitor, BookOpen } from '@element-plus/icons-vue';\nexport default {\n  __name: 'FeatureCards',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const router = useRouter();\n\n    // 功能卡片数据\n    const features = [{\n      id: 'lesson-generator',\n      title: '智能教案生成器',\n      description: '基于AI技术的智能教案生成工具，支持多学科、多年级的教案自动生成，包含教学目标、重点难点、教学步骤等完整内容。',\n      icon: Document,\n      route: '/jiaoan',\n      color: '#409eff',\n      bgColor: '#ecf5ff'\n    }, {\n      id: 'ai-chat',\n      title: 'AI学习助手',\n      description: 'AI驱动的智能问答系统，为师生提供实时的学习指导和答疑服务，支持多种学科知识点解析。',\n      icon: ChatDotRound,\n      route: '/aichat',\n      color: '#67c23a',\n      bgColor: '#f0f9eb'\n    }, {\n      id: 'ppt-generator',\n      title: 'PPT课件生成',\n      description: '快速生成精美的教学课件，支持多种模板和样式，让教学内容更加生动有趣。',\n      icon: Monitor,\n      route: '/pptgenerate',\n      color: '#e6a23c',\n      bgColor: '#fdf6ec'\n    }, {\n      id: 'knowledge-base',\n      title: '教学资源库',\n      description: '丰富的教学资源库，包含各学科的教学素材、习题库、参考资料等，助力教学质量提升。',\n      icon: BookOpen,\n      route: '/resources',\n      color: '#f56c6c',\n      bgColor: '#fef0f0'\n    }];\n\n    // 跳转到指定功能\n    const navigateToFeature = route => {\n      router.push(route);\n    };\n    const __returned__ = {\n      router,\n      features,\n      navigateToFeature,\n      get useRouter() {\n        return useRouter;\n      },\n      get Document() {\n        return Document;\n      },\n      get ChatDotRound() {\n        return ChatDotRound;\n      },\n      get Monitor() {\n        return Monitor;\n      },\n      get BookOpen() {\n        return BookOpen;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["useRouter", "Document", "ChatDotRound", "Monitor", "BookOpen", "router", "features", "id", "title", "description", "icon", "route", "color", "bgColor", "navigateToFeature", "push"], "sources": ["C:/Users/<USER>/Desktop/shishuo/vue/src/components/home/<USER>"], "sourcesContent": ["<script setup>\nimport { useRouter } from 'vue-router'\nimport { Document, ChatDotRound, Monitor, BookOpen } from '@element-plus/icons-vue'\n\nconst router = useRouter()\n\n// 功能卡片数据\nconst features = [\n  {\n    id: 'lesson-generator',\n    title: '智能教案生成器',\n    description: '基于AI技术的智能教案生成工具，支持多学科、多年级的教案自动生成，包含教学目标、重点难点、教学步骤等完整内容。',\n    icon: Document,\n    route: '/jiaoan',\n    color: '#409eff',\n    bgColor: '#ecf5ff'\n  },\n  {\n    id: 'ai-chat',\n    title: 'AI学习助手',\n    description: 'AI驱动的智能问答系统，为师生提供实时的学习指导和答疑服务，支持多种学科知识点解析。',\n    icon: ChatDotRound,\n    route: '/aichat',\n    color: '#67c23a',\n    bgColor: '#f0f9eb'\n  },\n  {\n    id: 'ppt-generator',\n    title: 'PPT课件生成',\n    description: '快速生成精美的教学课件，支持多种模板和样式，让教学内容更加生动有趣。',\n    icon: Monitor,\n    route: '/pptgenerate',\n    color: '#e6a23c',\n    bgColor: '#fdf6ec'\n  },\n  {\n    id: 'knowledge-base',\n    title: '教学资源库',\n    description: '丰富的教学资源库，包含各学科的教学素材、习题库、参考资料等，助力教学质量提升。',\n    icon: BookOpen,\n    route: '/resources',\n    color: '#f56c6c',\n    bgColor: '#fef0f0'\n  }\n]\n\n// 跳转到指定功能\nconst navigateToFeature = (route) => {\n  router.push(route)\n}\n</script>\n\n<template>\n  <div class=\"feature-cards-container\">\n    <div class=\"header\">\n      <h2>师说教学辅助系统</h2>\n      <p>智能教学助手，助力教育创新</p>\n    </div>\n\n    <div class=\"cards-grid\">\n      <div\n        v-for=\"feature in features\"\n        :key=\"feature.id\"\n        class=\"feature-card\"\n        :style=\"{ '--card-color': feature.color, '--card-bg': feature.bgColor }\"\n        @click=\"navigateToFeature(feature.route)\"\n      >\n        <div class=\"card-icon\">\n          <el-icon :size=\"40\" :color=\"feature.color\">\n            <component :is=\"feature.icon\" />\n          </el-icon>\n        </div>\n\n        <div class=\"card-content\">\n          <h3>{{ feature.title }}</h3>\n          <p>{{ feature.description }}</p>\n        </div>\n\n        <div class=\"card-action\">\n          <el-button type=\"primary\" :style=\"{ backgroundColor: feature.color, borderColor: feature.color }\">\n            立即体验\n          </el-button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.feature-cards-container {\n  padding: 40px 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n\n  .header {\n    text-align: center;\n    margin-bottom: 50px;\n\n    h2 {\n      font-size: 32px;\n      color: #303133;\n      margin-bottom: 10px;\n      font-weight: 600;\n    }\n\n    p {\n      font-size: 16px;\n      color: #606266;\n      margin: 0;\n    }\n  }\n\n  .cards-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n    gap: 30px;\n\n    .feature-card {\n      background: white;\n      border-radius: 12px;\n      padding: 30px 25px;\n      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n      cursor: pointer;\n      transition: all 0.3s ease;\n      border: 2px solid transparent;\n      position: relative;\n      overflow: hidden;\n\n      &::before {\n        content: '';\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        height: 4px;\n        background: var(--card-color);\n      }\n\n      &:hover {\n        transform: translateY(-8px);\n        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n        border-color: var(--card-color);\n\n        .card-icon {\n          transform: scale(1.1);\n        }\n      }\n\n      .card-icon {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 80px;\n        height: 80px;\n        background: var(--card-bg);\n        border-radius: 50%;\n        margin: 0 auto 20px;\n        transition: transform 0.3s ease;\n      }\n\n      .card-content {\n        text-align: center;\n        margin-bottom: 25px;\n\n        h3 {\n          font-size: 20px;\n          color: #303133;\n          margin-bottom: 15px;\n          font-weight: 600;\n        }\n\n        p {\n          font-size: 14px;\n          color: #606266;\n          line-height: 1.6;\n          margin: 0;\n        }\n      }\n\n      .card-action {\n        text-align: center;\n\n        .el-button {\n          width: 100%;\n          font-weight: 500;\n        }\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .feature-cards-container {\n    padding: 20px 15px;\n\n    .header {\n      margin-bottom: 30px;\n\n      h2 {\n        font-size: 24px;\n      }\n    }\n\n    .cards-grid {\n      grid-template-columns: 1fr;\n      gap: 20px;\n\n      .feature-card {\n        padding: 25px 20px;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";AACA,SAASA,SAAS,QAAQ,YAAW;AACrC,SAASC,QAAQ,EAAEC,YAAY,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,yBAAwB;;;;;;;IAElF,MAAMC,MAAM,GAAGL,SAAS,CAAC;;IAEzB;IACA,MAAMM,QAAQ,GAAG,CACf;MACEC,EAAE,EAAE,kBAAkB;MACtBC,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,yDAAyD;MACtEC,IAAI,EAAET,QAAQ;MACdU,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE,SAAS;MAChBC,OAAO,EAAE;IACX,CAAC,EACD;MACEN,EAAE,EAAE,SAAS;MACbC,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,4CAA4C;MACzDC,IAAI,EAAER,YAAY;MAClBS,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE,SAAS;MAChBC,OAAO,EAAE;IACX,CAAC,EACD;MACEN,EAAE,EAAE,eAAe;MACnBC,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,oCAAoC;MACjDC,IAAI,EAAEP,OAAO;MACbQ,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE,SAAS;MAChBC,OAAO,EAAE;IACX,CAAC,EACD;MACEN,EAAE,EAAE,gBAAgB;MACpBC,KAAK,EAAE,OAAO;MACdC,WAAW,EAAE,yCAAyC;MACtDC,IAAI,EAAEN,QAAQ;MACdO,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,SAAS;MAChBC,OAAO,EAAE;IACX,EACF;;IAEA;IACA,MAAMC,iBAAiB,GAAIH,KAAK,IAAK;MACnCN,MAAM,CAACU,IAAI,CAACJ,KAAK;IACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}