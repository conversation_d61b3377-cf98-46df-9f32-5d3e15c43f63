{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"header\"\n};\nconst _hoisted_2 = {\n  class: \"extra\"\n};\nconst _hoisted_3 = [\"src\"];\nconst _hoisted_4 = {\n  class: \"editor\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _component_el_drawer = _resolveComponent(\"el-drawer\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  return _openBlock(), _createBlock(_component_el_card, {\n    class: \"page-container\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_1, [_cache[13] || (_cache[13] = _createElementVNode(\"span\", null, \"文章管理\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[0] || (_cache[0] = $event => $setup.visibleDrawer = true)\n    }, {\n      default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"添加文章\")])),\n      _: 1 /* STABLE */\n    })])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      inline: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"文章分类：\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          placeholder: \"请选择\",\n          modelValue: $setup.categoryId,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.categoryId = $event)\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.categorys, c => {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: c.id,\n              label: c.categoryName,\n              value: c.id\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"发布状态：\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          placeholder: \"请选择\",\n          modelValue: $setup.state,\n          \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.state = $event)\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"已发布\",\n            value: \"已发布\"\n          }), _createVNode(_component_el_option, {\n            label: \"草稿\",\n            value: \"草稿\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, null, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: $setup.articleList\n        }, {\n          default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\"搜索\")])),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_button, {\n          onClick: _cache[3] || (_cache[3] = $event => {\n            $setup.categoryId = '';\n            $setup.state = '';\n          })\n        }, {\n          default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\"重置\")])),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table, {\n      data: $setup.articles,\n      style: {\n        \"width\": \"100%\"\n      }\n    }, {\n      empty: _withCtx(() => [_createVNode(_component_el_empty, {\n        description: \"没有数据\"\n      })]),\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        label: \"文章标题\",\n        width: \"400\",\n        prop: \"title\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"分类\",\n        prop: \"categoryName\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"发表时间\",\n        prop: \"createTime\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"状态\",\n        prop: \"state\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"100\"\n      }, {\n        default: _withCtx(({\n          row\n        }) => [_createVNode(_component_el_button, {\n          icon: $setup.Edit,\n          circle: \"\",\n          plain: \"\",\n          type: \"primary\",\n          onClick: $event => $setup.editArticle(row)\n        }, null, 8 /* PROPS */, [\"icon\", \"onClick\"]), _createVNode(_component_el_button, {\n          icon: $setup.Delete,\n          circle: \"\",\n          plain: \"\",\n          type: \"danger\",\n          onClick: $event => $setup.deleteArticle(row)\n        }, null, 8 /* PROPS */, [\"icon\", \"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\"]), _createVNode(_component_el_pagination, {\n      \"current-page\": $setup.pageNum,\n      \"onUpdate:currentPage\": _cache[4] || (_cache[4] = $event => $setup.pageNum = $event),\n      \"page-size\": $setup.pageSize,\n      \"onUpdate:pageSize\": _cache[5] || (_cache[5] = $event => $setup.pageSize = $event),\n      \"page-sizes\": [3, 5, 10, 15],\n      layout: \"jumper, total, sizes, prev, pager, next\",\n      background: \"\",\n      total: $setup.total,\n      onSizeChange: $setup.onSizeChange,\n      onCurrentChange: $setup.onCurrentChange,\n      style: {\n        \"margin-top\": \"20px\",\n        \"justify-content\": \"flex-end\"\n      }\n    }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\"]), _createVNode(_component_el_drawer, {\n      modelValue: $setup.visibleDrawer,\n      \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $setup.visibleDrawer = $event),\n      title: \"添加文章\",\n      direction: \"rtl\",\n      size: \"50%\"\n    }, {\n      default: _withCtx(() => [_createCommentVNode(\" 添加文章表单 \"), _createVNode(_component_el_form, {\n        model: $setup.articleModel,\n        \"label-width\": \"100px\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_form_item, {\n          label: \"文章标题\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            modelValue: $setup.articleModel.title,\n            \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.articleModel.title = $event),\n            placeholder: \"请输入标题\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"文章分类\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_select, {\n            placeholder: \"请选择\",\n            modelValue: $setup.articleModel.categoryId,\n            \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.articleModel.categoryId = $event)\n          }, {\n            default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.categorys, c => {\n              return _openBlock(), _createBlock(_component_el_option, {\n                key: c.id,\n                label: c.categoryName,\n                value: c.id\n              }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n            }), 128 /* KEYED_FRAGMENT */))]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"文章封面\"\n        }, {\n          default: _withCtx(() => [_createCommentVNode(\"\\r\\n                        auto-upload:设置是否自动上传\\r\\n                        action:设置服务器接口路径\\r\\n                        name:设置上传的文件字段名\\r\\n                        headers:设置上传的请求头\\r\\n                        on-success:设置上传成功的回调函数\\r\\n                     \"), _createVNode(_component_el_upload, {\n            class: \"avatar-uploader\",\n            \"auto-upload\": true,\n            \"show-file-list\": false,\n            action: \"/api/upload\",\n            name: \"file\",\n            headers: {\n              'Authorization': $setup.tokenStore.token\n            },\n            \"on-success\": $setup.uploadSuccess\n          }, {\n            default: _withCtx(() => [$setup.articleModel.coverImg ? (_openBlock(), _createElementBlock(\"img\", {\n              key: 0,\n              src: $setup.articleModel.coverImg,\n              class: \"avatar\"\n            }, null, 8 /* PROPS */, _hoisted_3)) : (_openBlock(), _createBlock(_component_el_icon, {\n              key: 1,\n              class: \"avatar-uploader-icon\"\n            }, {\n              default: _withCtx(() => [_createVNode($setup[\"Plus\"])]),\n              _: 1 /* STABLE */\n            }))]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"headers\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"文章内容\"\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createVNode($setup[\"QuillEditor\"], {\n            theme: \"snow\",\n            content: $setup.articleModel.content,\n            \"onUpdate:content\": _cache[8] || (_cache[8] = $event => $setup.articleModel.content = $event),\n            contentType: \"html\"\n          }, null, 8 /* PROPS */, [\"content\"])])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, null, {\n          default: _withCtx(() => [_createVNode(_component_el_button, {\n            type: \"primary\",\n            onClick: _cache[9] || (_cache[9] = $event => $setup.addArticle('已发布'))\n          }, {\n            default: _withCtx(() => _cache[16] || (_cache[16] = [_createTextVNode(\"发布\")])),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_button, {\n            type: \"info\",\n            onClick: _cache[10] || (_cache[10] = $event => $setup.addArticle('草稿'))\n          }, {\n            default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\"草稿\")])),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"model\"])]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])]),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_createVNode", "_component_el_button", "type", "onClick", "_cache", "$event", "$setup", "visibleDrawer", "default", "_createTextVNode", "_", "_component_el_form", "inline", "_component_el_form_item", "label", "_component_el_select", "placeholder", "modelValue", "categoryId", "_createElementBlock", "_Fragment", "_renderList", "categorys", "c", "_component_el_option", "key", "id", "categoryName", "value", "state", "articleList", "_component_el_table", "data", "articles", "style", "empty", "_component_el_empty", "description", "_component_el_table_column", "width", "prop", "row", "icon", "Edit", "circle", "plain", "editArticle", "Delete", "deleteArticle", "_component_el_pagination", "pageNum", "pageSize", "layout", "background", "total", "onSizeChange", "onCurrentChange", "_component_el_drawer", "title", "direction", "size", "_createCommentVNode", "model", "articleModel", "_component_el_input", "_component_el_upload", "action", "name", "headers", "tokenStore", "token", "uploadSuccess", "coverImg", "src", "_hoisted_3", "_component_el_icon", "_hoisted_4", "theme", "content", "contentType", "addArticle"], "sources": ["C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\article\\ArticleManage.vue"], "sourcesContent": ["<script setup>\r\nimport {\r\n    Edit,\r\n    Delete\r\n} from '@element-plus/icons-vue'\r\n\r\nimport { ref } from 'vue'\r\n\r\n//文章分类数据模型\r\nconst categorys = ref([\r\n    {\r\n        \"id\": 3,\r\n        \"categoryName\": \"美食\",\r\n        \"categoryAlias\": \"my\",\r\n        \"createTime\": \"2023-09-02 12:06:59\",\r\n        \"updateTime\": \"2023-09-02 12:06:59\"\r\n    },\r\n    {\r\n        \"id\": 4,\r\n        \"categoryName\": \"娱乐\",\r\n        \"categoryAlias\": \"yl\",\r\n        \"createTime\": \"2023-09-02 12:08:16\",\r\n        \"updateTime\": \"2023-09-02 12:08:16\"\r\n    },\r\n    {\r\n        \"id\": 5,\r\n        \"categoryName\": \"军事\",\r\n        \"categoryAlias\": \"js\",\r\n        \"createTime\": \"2023-09-02 12:08:33\",\r\n        \"updateTime\": \"2023-09-02 12:08:33\"\r\n    }\r\n])\r\n\r\n//用户搜索时选中的分类id\r\nconst categoryId = ref('')\r\n\r\n//用户搜索时选中的发布状态\r\nconst state = ref('')\r\n\r\n//文章列表数据模型\r\nconst articles = ref([\r\n    {\r\n        \"id\": 5,\r\n        \"title\": \"陕西旅游攻略\",\r\n        \"content\": \"兵马俑,华清池,法门寺,华山...爱去哪去哪...\",\r\n        \"coverImg\": \"https://big-event-gwd.oss-cn-beijing.aliyuncs.com/9bf1cf5b-1420-4c1b-91ad-e0f4631cbed4.png\",\r\n        \"state\": \"草稿\",\r\n        \"categoryId\": 2,\r\n        \"createTime\": \"2023-09-03 11:55:30\",\r\n        \"updateTime\": \"2023-09-03 11:55:30\"\r\n    },\r\n    {\r\n        \"id\": 5,\r\n        \"title\": \"陕西旅游攻略\",\r\n        \"content\": \"兵马俑,华清池,法门寺,华山...爱去哪去哪...\",\r\n        \"coverImg\": \"https://big-event-gwd.oss-cn-beijing.aliyuncs.com/9bf1cf5b-1420-4c1b-91ad-e0f4631cbed4.png\",\r\n        \"state\": \"草稿\",\r\n        \"categoryId\": 2,\r\n        \"createTime\": \"2023-09-03 11:55:30\",\r\n        \"updateTime\": \"2023-09-03 11:55:30\"\r\n    },\r\n    {\r\n        \"id\": 5,\r\n        \"title\": \"陕西旅游攻略\",\r\n        \"content\": \"兵马俑,华清池,法门寺,华山...爱去哪去哪...\",\r\n        \"coverImg\": \"https://big-event-gwd.oss-cn-beijing.aliyuncs.com/9bf1cf5b-1420-4c1b-91ad-e0f4631cbed4.png\",\r\n        \"state\": \"草稿\",\r\n        \"categoryId\": 2,\r\n        \"createTime\": \"2023-09-03 11:55:30\",\r\n        \"updateTime\": \"2023-09-03 11:55:30\"\r\n    },\r\n])\r\n\r\n//分页条数据模型\r\nconst pageNum = ref(1)//当前页\r\nconst total = ref(20)//总条数\r\nconst pageSize = ref(3)//每页条数\r\n\r\n//当每页条数发生了变化，调用此函数\r\nconst onSizeChange = (size) => {\r\n    pageSize.value = size\r\n    articleList()\r\n}\r\n//当前页码发生变化，调用此函数\r\nconst onCurrentChange = (num) => {\r\n    pageNum.value = num\r\n    articleList()\r\n}\r\n\r\n\r\n//回显文章分类\r\nimport { articleCategoryListService, articleListService,articleAddService } from '@/api/article.js'\r\nconst articleCategoryList = async () => {\r\n    let result = await articleCategoryListService();\r\n\r\n    categorys.value = result.data;\r\n}\r\n\r\n//获取文章列表数据\r\nconst articleList = async () => {\r\n    let params = {\r\n        pageNum: pageNum.value,\r\n        pageSize: pageSize.value,\r\n        categoryId: categoryId.value ? categoryId.value : null,\r\n        state: state.value ? state.value : null\r\n    }\r\n    let result = await articleListService(params);\r\n\r\n    //渲染视图\r\n    total.value = result.data.total;\r\n    articles.value = result.data.items;\r\n\r\n    //处理数据,给数据模型扩展一个属性categoryName,分类名称\r\n    for (let i = 0; i < articles.value.length; i++) {\r\n        let article = articles.value[i];\r\n        for (let j = 0; j < categorys.value.length; j++) {\r\n            if (article.categoryId == categorys.value[j].id) {\r\n                article.categoryName = categorys.value[j].categoryName;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\narticleCategoryList()\r\narticleList();\r\n\r\nimport { QuillEditor } from '@vueup/vue-quill'\r\nimport '@vueup/vue-quill/dist/vue-quill.snow.css'\r\nimport { Plus } from '@element-plus/icons-vue'\r\n//控制抽屉是否显示\r\nconst visibleDrawer = ref(false)\r\n//添加表单数据模型\r\nconst articleModel = ref({\r\n    title: '',\r\n    categoryId: '',\r\n    coverImg: '',\r\n    content: '',\r\n    state: ''\r\n})\r\n\r\n\r\n//导入token\r\nimport { useTokenStore } from '@/stores/token.js';\r\nconst tokenStore = useTokenStore();\r\n\r\n//上传成功的回调函数\r\nconst uploadSuccess = (result)=>{\r\n    articleModel.value.coverImg = result.data;\r\n    console.log(result.data);\r\n}\r\n\r\n//添加文章\r\nimport {ElMessage, ElMessageBox} from 'element-plus'\r\nconst addArticle = async (clickState)=>{\r\n    //把发布状态赋值给数据模型\r\n    articleModel.value.state = clickState;\r\n\r\n    //调用接口\r\n    let result = await articleAddService(articleModel.value);\r\n\r\n    ElMessage.success(result.msg? result.msg:'添加成功');\r\n\r\n    //让抽屉消失\r\n    visibleDrawer.value = false;\r\n\r\n    //刷新当前列表\r\n    articleList()\r\n}\r\n\r\n// 编辑文章\r\nconst editArticle = (row) => {\r\n    console.log('编辑文章', row)\r\n    // 这里可以添加编辑文章的逻辑\r\n    ElMessage.info('编辑文章功能正在开发中')\r\n}\r\n\r\n// 删除文章\r\nconst deleteArticle = (row) => {\r\n    console.log('删除文章', row)\r\n    // 这里可以添加删除文章的逻辑\r\n    ElMessageBox.confirm(\r\n        `确认删除文章 \"${row.title}\" 吗？`,\r\n        '删除提示',\r\n        {\r\n            confirmButtonText: '确认',\r\n            cancelButtonText: '取消',\r\n            type: 'warning',\r\n        }\r\n    )\r\n    .then(() => {\r\n        // 调用删除接口\r\n        ElMessage({\r\n            type: 'success',\r\n            message: '删除成功',\r\n        })\r\n        // 刷新列表\r\n        articleList()\r\n    })\r\n    .catch(() => {\r\n        ElMessage({\r\n            type: 'info',\r\n            message: '已取消删除',\r\n        })\r\n    })\r\n}\r\n</script>\r\n<template>\r\n    <el-card class=\"page-container\">\r\n        <template #header>\r\n            <div class=\"header\">\r\n                <span>文章管理</span>\r\n                <div class=\"extra\">\r\n                    <el-button type=\"primary\" @click=\"visibleDrawer = true\">添加文章</el-button>\r\n                </div>\r\n            </div>\r\n        </template>\r\n        <!-- 搜索表单 -->\r\n        <el-form inline>\r\n            <el-form-item label=\"文章分类：\">\r\n                <el-select placeholder=\"请选择\" v-model=\"categoryId\">\r\n                    <el-option v-for=\"c in categorys\" :key=\"c.id\" :label=\"c.categoryName\" :value=\"c.id\">\r\n                    </el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"发布状态：\">\r\n                <el-select placeholder=\"请选择\" v-model=\"state\">\r\n                    <el-option label=\"已发布\" value=\"已发布\"></el-option>\r\n                    <el-option label=\"草稿\" value=\"草稿\"></el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button type=\"primary\" @click=\"articleList\">搜索</el-button>\r\n                <el-button @click=\"categoryId = ''; state = ''\">重置</el-button>\r\n            </el-form-item>\r\n        </el-form>\r\n        <!-- 文章列表 -->\r\n        <el-table :data=\"articles\" style=\"width: 100%\">\r\n            <el-table-column label=\"文章标题\" width=\"400\" prop=\"title\"></el-table-column>\r\n            <el-table-column label=\"分类\" prop=\"categoryName\"></el-table-column>\r\n            <el-table-column label=\"发表时间\" prop=\"createTime\"> </el-table-column>\r\n            <el-table-column label=\"状态\" prop=\"state\"></el-table-column>\r\n            <el-table-column label=\"操作\" width=\"100\">\r\n                <template #default=\"{ row }\">\r\n                    <el-button :icon=\"Edit\" circle plain type=\"primary\" @click=\"editArticle(row)\"></el-button>\r\n                    <el-button :icon=\"Delete\" circle plain type=\"danger\" @click=\"deleteArticle(row)\"></el-button>\r\n                </template>\r\n            </el-table-column>\r\n            <template #empty>\r\n                <el-empty description=\"没有数据\" />\r\n            </template>\r\n        </el-table>\r\n        <!-- 分页条 -->\r\n        <el-pagination v-model:current-page=\"pageNum\" v-model:page-size=\"pageSize\" :page-sizes=\"[3, 5, 10, 15]\"\r\n            layout=\"jumper, total, sizes, prev, pager, next\" background :total=\"total\" @size-change=\"onSizeChange\"\r\n            @current-change=\"onCurrentChange\" style=\"margin-top: 20px; justify-content: flex-end\" />\r\n\r\n        <!-- 抽屉 -->\r\n        <el-drawer v-model=\"visibleDrawer\" title=\"添加文章\" direction=\"rtl\" size=\"50%\">\r\n            <!-- 添加文章表单 -->\r\n            <el-form :model=\"articleModel\" label-width=\"100px\">\r\n                <el-form-item label=\"文章标题\">\r\n                    <el-input v-model=\"articleModel.title\" placeholder=\"请输入标题\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"文章分类\">\r\n                    <el-select placeholder=\"请选择\" v-model=\"articleModel.categoryId\">\r\n                        <el-option v-for=\"c in categorys\" :key=\"c.id\" :label=\"c.categoryName\" :value=\"c.id\">\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"文章封面\">\r\n\r\n                    <!--\r\n                        auto-upload:设置是否自动上传\r\n                        action:设置服务器接口路径\r\n                        name:设置上传的文件字段名\r\n                        headers:设置上传的请求头\r\n                        on-success:设置上传成功的回调函数\r\n                     -->\r\n\r\n                    <el-upload class=\"avatar-uploader\" :auto-upload=\"true\" :show-file-list=\"false\"\r\n                    action=\"/api/upload\"\r\n                    name=\"file\"\r\n                    :headers=\"{'Authorization':tokenStore.token}\"\r\n                    :on-success=\"uploadSuccess\"\r\n                    >\r\n                        <img v-if=\"articleModel.coverImg\" :src=\"articleModel.coverImg\" class=\"avatar\" />\r\n                        <el-icon v-else class=\"avatar-uploader-icon\">\r\n                            <Plus />\r\n                        </el-icon>\r\n                    </el-upload>\r\n                </el-form-item>\r\n                <el-form-item label=\"文章内容\">\r\n                    <div class=\"editor\">\r\n                        <quill-editor theme=\"snow\" v-model:content=\"articleModel.content\" contentType=\"html\">\r\n                        </quill-editor>\r\n                    </div>\r\n                </el-form-item>\r\n                <el-form-item>\r\n                    <el-button type=\"primary\" @click=\"addArticle('已发布')\">发布</el-button>\r\n                    <el-button type=\"info\" @click=\"addArticle('草稿')\">草稿</el-button>\r\n                </el-form-item>\r\n            </el-form>\r\n        </el-drawer>\r\n    </el-card>\r\n</template>\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n    min-height: 100%;\r\n    box-sizing: border-box;\r\n\r\n    .header {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n    }\r\n}\r\n\r\n/* 抽屉样式 */\r\n.avatar-uploader {\r\n    :deep() {\r\n        .avatar {\r\n            width: 178px;\r\n            height: 178px;\r\n            display: block;\r\n        }\r\n\r\n        .el-upload {\r\n            border: 1px dashed var(--el-border-color);\r\n            border-radius: 6px;\r\n            cursor: pointer;\r\n            position: relative;\r\n            overflow: hidden;\r\n            transition: var(--el-transition-duration-fast);\r\n        }\r\n\r\n        .el-upload:hover {\r\n            border-color: var(--el-color-primary);\r\n        }\r\n\r\n        .el-icon.avatar-uploader-icon {\r\n            font-size: 28px;\r\n            color: #8c939d;\r\n            width: 178px;\r\n            height: 178px;\r\n            text-align: center;\r\n        }\r\n    }\r\n}\r\n\r\n.editor {\r\n    width: 100%;\r\n\r\n    :deep(.ql-editor) {\r\n        min-height: 200px;\r\n    }\r\n}\r\n</style>"], "mappings": ";;EAkNiBA,KAAK,EAAC;AAAQ;;EAEVA,KAAK,EAAC;AAAO;mBApNlC;;EAsSyBA,KAAK,EAAC;AAAQ;;;;;;;;;;;;;;;;uBAtFnCC,YAAA,CAiGUC,kBAAA;IAjGDF,KAAK,EAAC;EAAgB;IAChBG,MAAM,EAAAC,QAAA,CACb,MAKM,CALNC,mBAAA,CAKM,OALNC,UAKM,G,4BAJFD,mBAAA,CAAiB,cAAX,MAAI,sBACVA,mBAAA,CAEM,OAFNE,UAEM,GADFC,YAAA,CAAwEC,oBAAA;MAA7DC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAAC,aAAa;;MArNnEC,OAAA,EAAAZ,QAAA,CAqN4E,MAAIQ,MAAA,SAAAA,MAAA,QArNhFK,gBAAA,CAqN4E,MAAI,E;MArNhFC,CAAA;;IAAAF,OAAA,EAAAZ,QAAA,CA0NQ,MAkBU,CAlBVI,YAAA,CAkBUW,kBAAA;MAlBDC,MAAM,EAAN;IAAM;MA1NvBJ,OAAA,EAAAZ,QAAA,CA2NY,MAKe,CALfI,YAAA,CAKea,uBAAA;QALDC,KAAK,EAAC;MAAO;QA3NvCN,OAAA,EAAAZ,QAAA,CA4NgB,MAGY,CAHZI,YAAA,CAGYe,oBAAA;UAHDC,WAAW,EAAC,KAAK;UA5N5CC,UAAA,EA4NsDX,MAAA,CAAAY,UAAU;UA5NhE,uBAAAd,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA4NsDC,MAAA,CAAAY,UAAU,GAAAb,MAAA;;UA5NhEG,OAAA,EAAAZ,QAAA,CA6N+B,MAAsB,E,kBAAjCuB,mBAAA,CACYC,SAAA,QA9NhCC,WAAA,CA6N2Cf,MAAA,CAAAgB,SAAS,EAAdC,CAAC;iCAAnB9B,YAAA,CACY+B,oBAAA;cADuBC,GAAG,EAAEF,CAAC,CAACG,EAAE;cAAGZ,KAAK,EAAES,CAAC,CAACI,YAAY;cAAGC,KAAK,EAAEL,CAAC,CAACG;;;UA7NpGhB,CAAA;;QAAAA,CAAA;UAkOYV,YAAA,CAKea,uBAAA;QALDC,KAAK,EAAC;MAAO;QAlOvCN,OAAA,EAAAZ,QAAA,CAmOgB,MAGY,CAHZI,YAAA,CAGYe,oBAAA;UAHDC,WAAW,EAAC,KAAK;UAnO5CC,UAAA,EAmOsDX,MAAA,CAAAuB,KAAK;UAnO3D,uBAAAzB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAmOsDC,MAAA,CAAAuB,KAAK,GAAAxB,MAAA;;UAnO3DG,OAAA,EAAAZ,QAAA,CAoOoB,MAA+C,CAA/CI,YAAA,CAA+CwB,oBAAA;YAApCV,KAAK,EAAC,KAAK;YAACc,KAAK,EAAC;cAC7B5B,YAAA,CAA6CwB,oBAAA;YAAlCV,KAAK,EAAC,IAAI;YAACc,KAAK,EAAC;;UArOhDlB,CAAA;;QAAAA,CAAA;UAwOYV,YAAA,CAGea,uBAAA;QA3O3BL,OAAA,EAAAZ,QAAA,CAyOgB,MAA6D,CAA7DI,YAAA,CAA6DC,oBAAA;UAAlDC,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAEG,MAAA,CAAAwB;;UAzOlDtB,OAAA,EAAAZ,QAAA,CAyO+D,MAAEQ,MAAA,SAAAA,MAAA,QAzOjEK,gBAAA,CAyO+D,IAAE,E;UAzOjEC,CAAA;YA0OgBV,YAAA,CAA8DC,oBAAA;UAAlDE,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA;YAAEC,MAAA,CAAAY,UAAU;YAAOZ,MAAA,CAAAuB,KAAK;UAAA;;UA1OzDrB,OAAA,EAAAZ,QAAA,CA0OgE,MAAEQ,MAAA,SAAAA,MAAA,QA1OlEK,gBAAA,CA0OgE,IAAE,E;UA1OlEC,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QA8OQV,YAAA,CAcW+B,mBAAA;MAdAC,IAAI,EAAE1B,MAAA,CAAA2B,QAAQ;MAAEC,KAAmB,EAAnB;QAAA;MAAA;;MAWZC,KAAK,EAAAvC,QAAA,CACZ,MAA+B,CAA/BI,YAAA,CAA+BoC,mBAAA;QAArBC,WAAW,EAAC;MAAM,G;MA1P5C7B,OAAA,EAAAZ,QAAA,CA+OY,MAAyE,CAAzEI,YAAA,CAAyEsC,0BAAA;QAAxDxB,KAAK,EAAC,MAAM;QAACyB,KAAK,EAAC,KAAK;QAACC,IAAI,EAAC;UAC/CxC,YAAA,CAAkEsC,0BAAA;QAAjDxB,KAAK,EAAC,IAAI;QAAC0B,IAAI,EAAC;UACjCxC,YAAA,CAAmEsC,0BAAA;QAAlDxB,KAAK,EAAC,MAAM;QAAC0B,IAAI,EAAC;UACnCxC,YAAA,CAA2DsC,0BAAA;QAA1CxB,KAAK,EAAC,IAAI;QAAC0B,IAAI,EAAC;UACjCxC,YAAA,CAKkBsC,0BAAA;QALDxB,KAAK,EAAC,IAAI;QAACyB,KAAK,EAAC;;QACnB/B,OAAO,EAAAZ,QAAA,CACd,CAA0F;UADxE6C;QAAG,OACrBzC,YAAA,CAA0FC,oBAAA;UAA9EyC,IAAI,EAAEpC,MAAA,CAAAqC,IAAI;UAAEC,MAAM,EAAN,EAAM;UAACC,KAAK,EAAL,EAAK;UAAC3C,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAAE,MAAA,IAAEC,MAAA,CAAAwC,WAAW,CAACL,GAAG;sDAC3EzC,YAAA,CAA6FC,oBAAA;UAAjFyC,IAAI,EAAEpC,MAAA,CAAAyC,MAAM;UAAEH,MAAM,EAAN,EAAM;UAACC,KAAK,EAAL,EAAK;UAAC3C,IAAI,EAAC,QAAQ;UAAEC,OAAK,EAAAE,MAAA,IAAEC,MAAA,CAAA0C,aAAa,CAACP,GAAG;;QAtPlG/B,CAAA;;MAAAA,CAAA;iCA8PQV,YAAA,CAE4FiD,wBAAA;MAFrE,cAAY,EAAE3C,MAAA,CAAA4C,OAAO;MA9PpD,wBAAA9C,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA8P6CC,MAAA,CAAA4C,OAAO,GAAA7C,MAAA;MAAU,WAAS,EAAEC,MAAA,CAAA6C,QAAQ;MA9PjF,qBAAA/C,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA8PyEC,MAAA,CAAA6C,QAAQ,GAAA9C,MAAA;MAAG,YAAU,EAAE,cAAc;MAClG+C,MAAM,EAAC,yCAAyC;MAACC,UAAU,EAAV,EAAU;MAAEC,KAAK,EAAEhD,MAAA,CAAAgD,KAAK;MAAGC,YAAW,EAAEjD,MAAA,CAAAiD,YAAY;MACpGC,eAAc,EAAElD,MAAA,CAAAkD,eAAe;MAAEtB,KAAmD,EAAnD;QAAA;QAAA;MAAA;qEAGtClC,YAAA,CA6CYyD,oBAAA;MAhTpBxC,UAAA,EAmQ4BX,MAAA,CAAAC,aAAa;MAnQzC,uBAAAH,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAmQ4BC,MAAA,CAAAC,aAAa,GAAAF,MAAA;MAAEqD,KAAK,EAAC,MAAM;MAACC,SAAS,EAAC,KAAK;MAACC,IAAI,EAAC;;MAnQ7EpD,OAAA,EAAAZ,QAAA,CAoQY,MAAe,CAAfiE,mBAAA,YAAe,EACf7D,YAAA,CA0CUW,kBAAA;QA1CAmD,KAAK,EAAExD,MAAA,CAAAyD,YAAY;QAAE,aAAW,EAAC;;QArQvDvD,OAAA,EAAAZ,QAAA,CAsQgB,MAEe,CAFfI,YAAA,CAEea,uBAAA;UAFDC,KAAK,EAAC;QAAM;UAtQ1CN,OAAA,EAAAZ,QAAA,CAuQoB,MAAsE,CAAtEI,YAAA,CAAsEgE,mBAAA;YAvQ1F/C,UAAA,EAuQuCX,MAAA,CAAAyD,YAAY,CAACL,KAAK;YAvQzD,uBAAAtD,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAuQuCC,MAAA,CAAAyD,YAAY,CAACL,KAAK,GAAArD,MAAA;YAAEW,WAAW,EAAC;;UAvQvEN,CAAA;YAyQgBV,YAAA,CAKea,uBAAA;UALDC,KAAK,EAAC;QAAM;UAzQ1CN,OAAA,EAAAZ,QAAA,CA0QoB,MAGY,CAHZI,YAAA,CAGYe,oBAAA;YAHDC,WAAW,EAAC,KAAK;YA1QhDC,UAAA,EA0Q0DX,MAAA,CAAAyD,YAAY,CAAC7C,UAAU;YA1QjF,uBAAAd,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA0Q0DC,MAAA,CAAAyD,YAAY,CAAC7C,UAAU,GAAAb,MAAA;;YA1QjFG,OAAA,EAAAZ,QAAA,CA2QmC,MAAsB,E,kBAAjCuB,mBAAA,CACYC,SAAA,QA5QpCC,WAAA,CA2Q+Cf,MAAA,CAAAgB,SAAS,EAAdC,CAAC;mCAAnB9B,YAAA,CACY+B,oBAAA;gBADuBC,GAAG,EAAEF,CAAC,CAACG,EAAE;gBAAGZ,KAAK,EAAES,CAAC,CAACI,YAAY;gBAAGC,KAAK,EAAEL,CAAC,CAACG;;;YA3QxGhB,CAAA;;UAAAA,CAAA;YA+QgBV,YAAA,CAqBea,uBAAA;UArBDC,KAAK,EAAC;QAAM;UA/Q1CN,OAAA,EAAAZ,QAAA,CAiRoB,MAMI,CANJiE,mBAAA,kQAMI,EAEJ7D,YAAA,CAUYiE,oBAAA;YAVDzE,KAAK,EAAC,iBAAiB;YAAE,aAAW,EAAE,IAAI;YAAG,gBAAc,EAAE,KAAK;YAC7E0E,MAAM,EAAC,aAAa;YACpBC,IAAI,EAAC,MAAM;YACVC,OAAO;cAAA,iBAAmB9D,MAAA,CAAA+D,UAAU,CAACC;YAAK;YAC1C,YAAU,EAAEhE,MAAA,CAAAiE;;YA7RjC/D,OAAA,EAAAZ,QAAA,CAoGg5L,MAAgF,CA2L77LU,MAAA,CAAAyD,YAAY,CAACS,QAAQ,I,cAAhCrD,mBAAA,CAAgF;cA/RxGM,GAAA;cA+R2DgD,GAAG,EAAEnE,MAAA,CAAAyD,YAAY,CAACS,QAAQ;cAAEhF,KAAK,EAAC;oCA/R7FkF,UAAA,M,cAgSwBjF,YAAA,CAEUkF,kBAAA;cAlSlClD,GAAA;cAgSwCjC,KAAK,EAAC;;cAhS9CgB,OAAA,EAAAZ,QAAA,CAiS4B,MAAQ,CAARI,YAAA,CAAQM,MAAA,U;cAjSpCI,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;YAqSgBV,YAAA,CAKea,uBAAA;UALDC,KAAK,EAAC;QAAM;UArS1CN,OAAA,EAAAZ,QAAA,CAsSoB,MAGM,CAHNC,mBAAA,CAGM,OAHN+E,UAGM,GAFF5E,YAAA,CACeM,MAAA;YADDuE,KAAK,EAAC,MAAM;YAASC,OAAO,EAAExE,MAAA,CAAAyD,YAAY,CAACe,OAAO;YAvSxF,oBAAA1E,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAuSoEC,MAAA,CAAAyD,YAAY,CAACe,OAAO,GAAAzE,MAAA;YAAE0E,WAAW,EAAC;;UAvStGrE,CAAA;YA2SgBV,YAAA,CAGea,uBAAA;UA9S/BL,OAAA,EAAAZ,QAAA,CA4SoB,MAAmE,CAAnEI,YAAA,CAAmEC,oBAAA;YAAxDC,IAAI,EAAC,SAAS;YAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAA0E,UAAU;;YA5ShExE,OAAA,EAAAZ,QAAA,CA4SyE,MAAEQ,MAAA,SAAAA,MAAA,QA5S3EK,gBAAA,CA4SyE,IAAE,E;YA5S3EC,CAAA;cA6SoBV,YAAA,CAA+DC,oBAAA;YAApDC,IAAI,EAAC,MAAM;YAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,MAAA,CAAA0E,UAAU;;YA7S7DxE,OAAA,EAAAZ,QAAA,CA6SqE,MAAEQ,MAAA,SAAAA,MAAA,QA7SvEK,gBAAA,CA6SqE,IAAE,E;YA7SvEC,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}