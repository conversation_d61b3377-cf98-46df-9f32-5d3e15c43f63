{"ast": null, "code": "import { createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createVNode($setup[\"WelcomeItem\"], null, {\n    icon: _withCtx(() => [_createVNode($setup[\"DocumentationIcon\"])]),\n    heading: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"在线错题本\")])),\n    default: _withCtx(() => [_cache[1] || (_cache[1] = _createTextVNode(\" 在线错题本功能可以帮助学生高效记录和管理错误题目，支持分类整理、重点标记和复习提醒。通过数据分析，帮助学生找到薄弱知识点。 \"))]),\n    _: 1 /* STABLE */\n  }), _createVNode($setup[\"WelcomeItem\"], null, {\n    icon: _withCtx(() => [_createVNode($setup[\"ToolingIcon\"])]),\n    heading: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\"智能教案\")])),\n    default: _withCtx(() => [_cache[3] || (_cache[3] = _createTextVNode(\" 智能教案功能为教师提供强大的备课工具，支持自动生成教学计划、资源推荐和课堂互动设计。同时支持个性化调整，满足不同教学需求。 \"))]),\n    _: 1 /* STABLE */\n  }), _createVNode($setup[\"WelcomeItem\"], null, {\n    icon: _withCtx(() => [_createVNode($setup[\"EcosystemIcon\"])]),\n    heading: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"AI学习助手\")])),\n    default: _withCtx(() => [_cache[5] || (_cache[5] = _createTextVNode(\" AI学习助手利用人工智能技术，为学生提供个性化的学习建议和实时答疑服务。无论是知识点解析还是学习路径规划，都能助你事半功倍。 \"))]),\n    _: 1 /* STABLE */\n  }), _createVNode($setup[\"WelcomeItem\"], null, {\n    icon: _withCtx(() => [_createVNode($setup[\"CommunityIcon\"])]),\n    heading: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"讨论区\")])),\n    default: _withCtx(() => [_cache[7] || (_cache[7] = _createTextVNode(\" 讨论区是师生交流的重要平台，学生可以在这里提问、分享学习心得，教师也可以参与讨论，解答疑惑。让我们一起构建一个充满活力的学习社区！ \"))]),\n    _: 1 /* STABLE */\n  }), _createVNode($setup[\"WelcomeItem\"], null, {\n    icon: _withCtx(() => [_createVNode($setup[\"SupportIcon\"])]),\n    heading: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\"语言风格的选择\")])),\n    default: _withCtx(() => [_cache[9] || (_cache[9] = _createTextVNode(\" 我们支持多种语言风格的教学内容，无论是严谨学术风还是轻松活泼风，都可以根据学生的需求进行选择和切换，打造最适合的学习体验。 \"))]),\n    _: 1 /* STABLE */\n  })], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["_createElementBlock", "_Fragment", "_createVNode", "$setup", "icon", "_withCtx", "heading", "_cache", "_createTextVNode", "default", "_"], "sources": ["C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\homeRight.vue"], "sourcesContent": ["<script setup>\nimport { useRouter } from 'vue-router'\nimport WelcomeItem from './WelcomeItem.vue'\nimport DocumentationIcon from '../icons/IconDocumentation.vue'\nimport ToolingIcon from '../icons/IconTooling.vue'\nimport EcosystemIcon from '../icons/IconEcosystem.vue'\nimport CommunityIcon from '../icons/IconCommunity.vue'\nimport SupportIcon from '../icons/IconSupport.vue'\n\nconst router = useRouter()\n\n// 跳转到教案生成器\nconst goToLessonGenerator = () => {\n  router.push('/lesson/generator')\n}\n\n// 跳转到AI聊天\nconst goToAIChat = () => {\n  router.push('/aichat')\n}\n\n// 跳转到PPT生成\nconst goToPPTGenerate = () => {\n  router.push('/pptgenerate')\n}\n</script>\n\n<template>\n  <WelcomeItem>\n    <template #icon>\n      <DocumentationIcon />\n    </template>\n    <template #heading>在线错题本</template>\n\n    在线错题本功能可以帮助学生高效记录和管理错误题目，支持分类整理、重点标记和复习提醒。通过数据分析，帮助学生找到薄弱知识点。\n  </WelcomeItem>\n\n  <WelcomeItem>\n    <template #icon>\n      <ToolingIcon />\n    </template>\n    <template #heading>智能教案</template>\n\n    智能教案功能为教师提供强大的备课工具，支持自动生成教学计划、资源推荐和课堂互动设计。同时支持个性化调整，满足不同教学需求。\n  </WelcomeItem>\n\n  <WelcomeItem>\n    <template #icon>\n      <EcosystemIcon />\n    </template>\n    <template #heading>AI学习助手</template>\n\n    AI学习助手利用人工智能技术，为学生提供个性化的学习建议和实时答疑服务。无论是知识点解析还是学习路径规划，都能助你事半功倍。\n  </WelcomeItem>\n\n  <WelcomeItem>\n    <template #icon>\n      <CommunityIcon />\n    </template>\n    <template #heading>讨论区</template>\n\n    讨论区是师生交流的重要平台，学生可以在这里提问、分享学习心得，教师也可以参与讨论，解答疑惑。让我们一起构建一个充满活力的学习社区！\n  </WelcomeItem>\n\n  <WelcomeItem>\n    <template #icon>\n      <SupportIcon />\n    </template>\n    <template #heading>语言风格的选择</template>\n\n    我们支持多种语言风格的教学内容，无论是严谨学术风还是轻松活泼风，都可以根据学生的需求进行选择和切换，打造最适合的学习体验。\n  </WelcomeItem>\n</template>"], "mappings": ";;uBAAAA,mBAAA,CAAAC,SAAA,SA4BEC,YAAA,CAOcC,MAAA;IANDC,IAAI,EAAAC,QAAA,CACb,MAAqB,CAArBH,YAAA,CAAqBC,MAAA,uB;IAEZG,OAAO,EAAAD,QAAA,CAAC,MAAKE,MAAA,QAAAA,MAAA,OAhC5BC,gBAAA,CAgCuB,OAAK,E;IAhC5BC,OAAA,EAAAJ,QAAA,CAgCuC,MAGrC,C,0BAnCFG,gBAAA,CAgCuC,iEAGrC,G;IAnCFE,CAAA;MAqCER,YAAA,CAOcC,MAAA;IANDC,IAAI,EAAAC,QAAA,CACb,MAAe,CAAfH,YAAA,CAAeC,MAAA,iB;IAENG,OAAO,EAAAD,QAAA,CAAC,MAAIE,MAAA,QAAAA,MAAA,OAzC3BC,gBAAA,CAyCuB,MAAI,E;IAzC3BC,OAAA,EAAAJ,QAAA,CAyCsC,MAGpC,C,0BA5CFG,gBAAA,CAyCsC,iEAGpC,G;IA5CFE,CAAA;MA8CER,YAAA,CAOcC,MAAA;IANDC,IAAI,EAAAC,QAAA,CACb,MAAiB,CAAjBH,YAAA,CAAiBC,MAAA,mB;IAERG,OAAO,EAAAD,QAAA,CAAC,MAAME,MAAA,QAAAA,MAAA,OAlD7BC,gBAAA,CAkDuB,QAAM,E;IAlD7BC,OAAA,EAAAJ,QAAA,CAkDwC,MAGtC,C,0BArDFG,gBAAA,CAkDwC,kEAGtC,G;IArDFE,CAAA;MAuDER,YAAA,CAOcC,MAAA;IANDC,IAAI,EAAAC,QAAA,CACb,MAAiB,CAAjBH,YAAA,CAAiBC,MAAA,mB;IAERG,OAAO,EAAAD,QAAA,CAAC,MAAGE,MAAA,QAAAA,MAAA,OA3D1BC,gBAAA,CA2DuB,KAAG,E;IA3D1BC,OAAA,EAAAJ,QAAA,CA2DqC,MAGnC,C,0BA9DFG,gBAAA,CA2DqC,qEAGnC,G;IA9DFE,CAAA;MAgEER,YAAA,CAOcC,MAAA;IANDC,IAAI,EAAAC,QAAA,CACb,MAAe,CAAfH,YAAA,CAAeC,MAAA,iB;IAENG,OAAO,EAAAD,QAAA,CAAC,MAAOE,MAAA,QAAAA,MAAA,OApE9BC,gBAAA,CAoEuB,SAAO,E;IApE9BC,OAAA,EAAAJ,QAAA,CAoEyC,MAGvC,C,0BAvEFG,gBAAA,CAoEyC,iEAGvC,G;IAvEFE,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}