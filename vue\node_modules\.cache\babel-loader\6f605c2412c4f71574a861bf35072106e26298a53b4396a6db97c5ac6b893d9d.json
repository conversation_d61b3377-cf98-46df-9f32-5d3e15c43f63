{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, with<PERSON>eys as _withKeys } from \"vue\";\nconst _hoisted_1 = {\n  class: \"header\"\n};\nconst _hoisted_2 = {\n  class: \"title\"\n};\nconst _hoisted_3 = {\n  class: \"actions\"\n};\nconst _hoisted_4 = {\n  class: \"objective-input\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"objectives-list\"\n};\nconst _hoisted_6 = {\n  class: \"step-input\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"steps-list\"\n};\nconst _hoisted_8 = {\n  class: \"step-header\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Document = _resolveComponent(\"Document\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_Edit = _resolveComponent(\"Edit\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_Download = _resolveComponent(\"Download\");\n  const _component_Loading = _resolveComponent(\"Loading\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_input_number = _resolveComponent(\"el-input-number\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  const _component_el_checkbox_group = _resolveComponent(\"el-checkbox-group\");\n  const _component_el_timeline_item = _resolveComponent(\"el-timeline-item\");\n  const _component_el_timeline = _resolveComponent(\"el-timeline\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_LessonPlanPreview = _resolveComponent(\"LessonPlanPreview\");\n  return _openBlock(), _createBlock(_component_el_card, {\n    class: \"lesson-plan-container\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"span\", _hoisted_2, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Document)]),\n      _: 1 /* STABLE */\n    }), _cache[14] || (_cache[14] = _createTextVNode(\" 智能教案生成器 \"))]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n      type: \"info\",\n      onClick: $setup.previewLessonPlan\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Edit)]),\n        _: 1 /* STABLE */\n      }), _cache[15] || (_cache[15] = _createTextVNode(\" 预览 \"))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      type: \"warning\",\n      onClick: $setup.resetForm\n    }, {\n      default: _withCtx(() => _cache[16] || (_cache[16] = [_createTextVNode(\" 重置 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.generateLessonPlan,\n      loading: $setup.isGenerating,\n      disabled: $setup.isGenerating\n    }, {\n      default: _withCtx(() => [!$setup.isGenerating ? (_openBlock(), _createBlock(_component_el_icon, {\n        key: 0\n      }, {\n        default: _withCtx(() => [_createVNode(_component_Download)]),\n        _: 1 /* STABLE */\n      })) : (_openBlock(), _createBlock(_component_el_icon, {\n        key: 1\n      }, {\n        default: _withCtx(() => [_createVNode(_component_Loading)]),\n        _: 1 /* STABLE */\n      })), _createTextVNode(\" \" + _toDisplayString($setup.isGenerating ? '生成中...' : '生成教案'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"loading\", \"disabled\"])])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.formData,\n      \"label-width\": \"120px\",\n      class: \"lesson-form\"\n    }, {\n      default: _withCtx(() => [_createCommentVNode(\" 基本信息 \"), _createVNode(_component_el_card, {\n        class: \"form-section\",\n        shadow: \"never\"\n      }, {\n        header: _withCtx(() => _cache[17] || (_cache[17] = [_createElementVNode(\"h3\", null, \"基本信息\", -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createVNode(_component_el_row, {\n          gutter: 20\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_col, {\n            span: 8\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_form_item, {\n              label: \"学科\",\n              required: \"\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_el_select, {\n                modelValue: $setup.formData.subject,\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.formData.subject = $event),\n                placeholder: \"请选择学科\",\n                style: {\n                  \"width\": \"100%\"\n                }\n              }, {\n                default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.subjectOptions, subject => {\n                  return _openBlock(), _createBlock(_component_el_option, {\n                    key: subject,\n                    label: subject,\n                    value: subject\n                  }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n                }), 128 /* KEYED_FRAGMENT */))]),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_col, {\n            span: 8\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_form_item, {\n              label: \"年级\",\n              required: \"\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_el_select, {\n                modelValue: $setup.formData.grade,\n                \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.formData.grade = $event),\n                placeholder: \"请选择年级\",\n                style: {\n                  \"width\": \"100%\"\n                }\n              }, {\n                default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.gradeOptions, grade => {\n                  return _openBlock(), _createBlock(_component_el_option, {\n                    key: grade,\n                    label: grade,\n                    value: grade\n                  }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n                }), 128 /* KEYED_FRAGMENT */))]),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_col, {\n            span: 8\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_form_item, {\n              label: \"课时长度\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_el_input_number, {\n                modelValue: $setup.formData.duration,\n                \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.formData.duration = $event),\n                min: 10,\n                max: 120,\n                step: 5,\n                style: {\n                  \"width\": \"100%\"\n                }\n              }, null, 8 /* PROPS */, [\"modelValue\"]), _cache[18] || (_cache[18] = _createElementVNode(\"span\", {\n                style: {\n                  \"margin-left\": \"8px\"\n                }\n              }, \"分钟\", -1 /* HOISTED */))]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"课题\",\n          required: \"\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            modelValue: $setup.formData.topic,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.formData.topic = $event),\n            placeholder: \"请输入课题名称\",\n            maxlength: \"100\",\n            \"show-word-limit\": \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 教学目标 \"), _createVNode(_component_el_card, {\n        class: \"form-section\",\n        shadow: \"never\"\n      }, {\n        header: _withCtx(() => _cache[19] || (_cache[19] = [_createElementVNode(\"h3\", null, \"教学目标\", -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_input, {\n          modelValue: $setup.objectiveInput,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.objectiveInput = $event),\n          placeholder: \"请输入教学目标\",\n          onKeyup: _withKeys($setup.addObjective, [\"enter\"])\n        }, {\n          append: _withCtx(() => [_createVNode(_component_el_button, {\n            onClick: $setup.addObjective,\n            icon: $setup.Plus\n          }, {\n            default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\"添加\")])),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"onClick\", \"icon\"])]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"])]), $setup.formData.objectives.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.formData.objectives, (objective, index) => {\n          return _openBlock(), _createBlock(_component_el_tag, {\n            key: index,\n            closable: \"\",\n            onClose: $event => $setup.removeObjective(index),\n            class: \"objective-tag\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString(objective), 1 /* TEXT */)]),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClose\"]);\n        }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 重点难点 \"), _createVNode(_component_el_card, {\n        class: \"form-section\",\n        shadow: \"never\"\n      }, {\n        header: _withCtx(() => _cache[21] || (_cache[21] = [_createElementVNode(\"h3\", null, \"重点难点\", -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createVNode(_component_el_form_item, {\n          label: \"教学重点\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            modelValue: $setup.formData.keyPoints,\n            \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.formData.keyPoints = $event),\n            type: \"textarea\",\n            rows: 3,\n            placeholder: \"请描述本课的教学重点和难点\",\n            maxlength: \"500\",\n            \"show-word-limit\": \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 教学方法 \"), _createVNode(_component_el_card, {\n        class: \"form-section\",\n        shadow: \"never\"\n      }, {\n        header: _withCtx(() => _cache[22] || (_cache[22] = [_createElementVNode(\"h3\", null, \"教学方法\", -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createVNode(_component_el_form_item, {\n          label: \"教学方法\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_checkbox_group, {\n            modelValue: $setup.formData.methods,\n            \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.formData.methods = $event)\n          }, {\n            default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.methodOptions, method => {\n              return _openBlock(), _createBlock(_component_el_checkbox, {\n                key: method.value,\n                label: method.value\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString(method.label), 1 /* TEXT */)]),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n            }), 128 /* KEYED_FRAGMENT */))]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"教学材料\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            modelValue: $setup.formData.materials,\n            \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.formData.materials = $event),\n            type: \"textarea\",\n            rows: 2,\n            placeholder: \"请列出所需的教学材料和设备\",\n            maxlength: \"300\",\n            \"show-word-limit\": \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 教学步骤 \"), _createVNode(_component_el_card, {\n        class: \"form-section\",\n        shadow: \"never\"\n      }, {\n        header: _withCtx(() => _cache[23] || (_cache[23] = [_createElementVNode(\"h3\", null, \"教学步骤\", -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_row, {\n          gutter: 10\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_col, {\n            span: 6\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_input, {\n              modelValue: $setup.stepInput.title,\n              \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.stepInput.title = $event),\n              placeholder: \"步骤标题\",\n              maxlength: \"50\"\n            }, null, 8 /* PROPS */, [\"modelValue\"])]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_col, {\n            span: 12\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_input, {\n              modelValue: $setup.stepInput.content,\n              \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $setup.stepInput.content = $event),\n              placeholder: \"步骤内容\",\n              maxlength: \"200\"\n            }, null, 8 /* PROPS */, [\"modelValue\"])]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_col, {\n            span: 4\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_input_number, {\n              modelValue: $setup.stepInput.time,\n              \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.stepInput.time = $event),\n              min: 1,\n              max: 60,\n              placeholder: \"时长\",\n              style: {\n                \"width\": \"100%\"\n              }\n            }, null, 8 /* PROPS */, [\"modelValue\"])]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_col, {\n            span: 2\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_button, {\n              onClick: $setup.addStep,\n              icon: $setup.Plus,\n              type: \"primary\"\n            }, {\n              default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\"添加\")])),\n              _: 1 /* STABLE */\n            }, 8 /* PROPS */, [\"onClick\", \"icon\"])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]), $setup.formData.steps.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createVNode(_component_el_timeline, null, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.formData.steps, (step, index) => {\n            return _openBlock(), _createBlock(_component_el_timeline_item, {\n              key: index,\n              timestamp: `${step.time}分钟`\n            }, {\n              default: _withCtx(() => [_createVNode(_component_el_card, {\n                class: \"step-card\"\n              }, {\n                default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"h4\", null, _toDisplayString(step.title), 1 /* TEXT */), _createVNode(_component_el_button, {\n                  onClick: $event => $setup.removeStep(index),\n                  icon: $setup.Delete,\n                  size: \"small\",\n                  type: \"danger\",\n                  text: \"\"\n                }, null, 8 /* PROPS */, [\"onClick\", \"icon\"])]), _createElementVNode(\"p\", null, _toDisplayString(step.content), 1 /* TEXT */)]),\n                _: 2 /* DYNAMIC */\n              }, 1024 /* DYNAMIC_SLOTS */)]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"timestamp\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        })])) : _createCommentVNode(\"v-if\", true)]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 作业与反思 \"), _createVNode(_component_el_card, {\n        class: \"form-section\",\n        shadow: \"never\"\n      }, {\n        header: _withCtx(() => _cache[25] || (_cache[25] = [_createElementVNode(\"h3\", null, \"作业与反思\", -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createVNode(_component_el_form_item, {\n          label: \"作业布置\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            modelValue: $setup.formData.homework,\n            \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $setup.formData.homework = $event),\n            type: \"textarea\",\n            rows: 3,\n            placeholder: \"请描述课后作业安排\",\n            maxlength: \"300\",\n            \"show-word-limit\": \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"教学反思\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            modelValue: $setup.formData.reflection,\n            \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $setup.formData.reflection = $event),\n            type: \"textarea\",\n            rows: 3,\n            placeholder: \"请填写教学反思（可在课后补充）\",\n            maxlength: \"500\",\n            \"show-word-limit\": \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"]), _createVNode(_component_LessonPlanPreview, {\n      visible: $setup.showPreview,\n      \"onUpdate:visible\": _cache[13] || (_cache[13] = $event => $setup.showPreview = $event),\n      \"lesson-plan\": $setup.formData,\n      onExport: $setup.handleExport,\n      onPrint: $setup.handlePrint\n    }, null, 8 /* PROPS */, [\"visible\", \"lesson-plan\", \"onExport\", \"onPrint\"])]),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "key", "_createBlock", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_createVNode", "_component_el_icon", "default", "_component_Document", "_", "_createTextVNode", "_hoisted_3", "_component_el_button", "type", "onClick", "$setup", "previewLessonPlan", "_component_Edit", "resetForm", "_cache", "generateLessonPlan", "loading", "isGenerating", "disabled", "_component_Download", "_component_Loading", "_toDisplayString", "_component_el_form", "model", "formData", "_createCommentVNode", "shadow", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_form_item", "label", "required", "_component_el_select", "modelValue", "subject", "$event", "placeholder", "style", "_createElementBlock", "_Fragment", "_renderList", "subjectOptions", "_component_el_option", "value", "grade", "gradeOptions", "_component_el_input_number", "duration", "min", "max", "step", "_component_el_input", "topic", "maxlength", "_hoisted_4", "objectiveInput", "onKeyup", "_with<PERSON><PERSON><PERSON>", "addObjective", "append", "icon", "Plus", "objectives", "length", "_hoisted_5", "objective", "index", "_component_el_tag", "closable", "onClose", "removeObjective", "keyPoints", "rows", "_component_el_checkbox_group", "methods", "methodOptions", "method", "_component_el_checkbox", "materials", "_hoisted_6", "stepInput", "title", "content", "time", "addStep", "steps", "_hoisted_7", "_component_el_timeline", "_component_el_timeline_item", "timestamp", "_hoisted_8", "removeStep", "Delete", "size", "text", "homework", "reflection", "_component_LessonPlanPreview", "visible", "showPreview", "onExport", "handleExport", "onPrint", "handlePrint"], "sources": ["C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\LessonPlanGenerator.vue"], "sourcesContent": ["<script>\nimport { ref, reactive } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { Document, Download, Plus, Delete, Edit, Loading } from '@element-plus/icons-vue'\nimport { generateLessonPlanService } from '@/api/lessonPlan.js'\n// import { saveLessonPlanService } from '@/api/lessonPlan.js' // 暂时注释，将来可能会用到\nimport LessonPlanPreview from '@/components/LessonPlanPreview.vue'\n\nexport default {\n  name: 'LessonPlanGenerator',\n  components: {\n    LessonPlanPreview\n  },\n  setup() {\n    // 表单数据\n    const formData = reactive({\n      subject: '', // 学科\n      grade: '', // 年级\n      topic: '', // 课题\n      duration: 45, // 课时长度（分钟）\n      objectives: [], // 教学目标\n      keyPoints: '', // 重点难点\n      materials: '', // 教学材料\n      methods: [], // 教学方法\n      steps: [], // 教学步骤\n      homework: '', // 作业布置\n      reflection: '' // 教学反思\n    })\n\n    // 教学目标列表\n    const objectiveInput = ref('')\n\n    // 教学方法选项\n    const methodOptions = [\n      { label: '讲授法', value: 'lecture' },\n      { label: '讨论法', value: 'discussion' },\n      { label: '实验法', value: 'experiment' },\n      { label: '演示法', value: 'demonstration' },\n      { label: '练习法', value: 'practice' },\n      { label: '案例分析法', value: 'case_study' },\n      { label: '小组合作', value: 'group_work' },\n      { label: '多媒体教学', value: 'multimedia' }\n    ]\n\n    // 教学步骤\n    const stepInput = reactive({\n      title: '',\n      content: '',\n      time: 5\n    })\n\n    // 学科选项\n    const subjectOptions = [\n      '语文', '数学', '英语', '物理', '化学', '生物',\n      '历史', '地理', '政治', '音乐', '美术', '体育', '信息技术'\n    ]\n\n    // 年级选项\n    const gradeOptions = [\n      '一年级', '二年级', '三年级', '四年级', '五年级', '六年级',\n      '七年级', '八年级', '九年级', '高一', '高二', '高三'\n    ]\n\n    // 添加教学目标\n    const addObjective = () => {\n      if (objectiveInput.value.trim()) {\n        formData.objectives.push(objectiveInput.value.trim())\n        objectiveInput.value = ''\n      }\n    }\n\n    // 删除教学目标\n    const removeObjective = (index) => {\n      formData.objectives.splice(index, 1)\n    }\n\n    // 添加教学步骤\n    const addStep = () => {\n      if (stepInput.title.trim() && stepInput.content.trim()) {\n        formData.steps.push({\n          title: stepInput.title,\n          content: stepInput.content,\n          time: stepInput.time\n        })\n        stepInput.title = ''\n        stepInput.content = ''\n        stepInput.time = 5\n      }\n    }\n\n    // 删除教学步骤\n    const removeStep = (index) => {\n      formData.steps.splice(index, 1)\n    }\n\n    // 生成状态\n    const isGenerating = ref(false)\n\n    // 预览状态\n    const showPreview = ref(false)\n\n    // 生成教案\n    const generateLessonPlan = async () => {\n      // 验证必填字段\n      if (!formData.subject || !formData.grade || !formData.topic) {\n        ElMessage.error('请填写学科、年级和课题')\n        return\n      }\n\n      if (formData.objectives.length === 0) {\n        ElMessage.error('请至少添加一个教学目标')\n        return\n      }\n\n      if (formData.steps.length === 0) {\n        ElMessage.error('请至少添加一个教学步骤')\n        return\n      }\n\n      try {\n        isGenerating.value = true\n        ElMessage.info('教案生成中，请稍候...')\n\n        // 调用后端API生成教案\n        const result = await generateLessonPlanService(formData)\n\n        if (result.code === 0) {\n          ElMessage.success('教案生成成功！')\n          // 可以在这里处理生成的教案数据\n          console.log('生成的教案:', result.data)\n        } else {\n          ElMessage.error(result.message || '教案生成失败')\n        }\n\n      } catch (error) {\n        console.error('教案生成错误:', error)\n        ElMessage.error('教案生成失败：' + (error.message || '网络错误'))\n      } finally {\n        isGenerating.value = false\n      }\n    }\n\n    // 重置表单\n    const resetForm = () => {\n      ElMessageBox.confirm('确认重置所有内容吗？', '提示', {\n        confirmButtonText: '确认',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        Object.assign(formData, {\n          subject: '',\n          grade: '',\n          topic: '',\n          duration: 45,\n          objectives: [],\n          keyPoints: '',\n          materials: '',\n          methods: [],\n          steps: [],\n          homework: '',\n          reflection: ''\n        })\n        objectiveInput.value = ''\n        ElMessage.success('表单已重置')\n      })\n    }\n\n    // 预览教案\n    const previewLessonPlan = () => {\n      // 验证基本信息\n      if (!formData.subject || !formData.grade || !formData.topic) {\n        ElMessage.error('请先填写基本信息（学科、年级、课题）')\n        return\n      }\n\n      showPreview.value = true\n    }\n\n    // 导出教案\n    const handleExport = (format) => {\n      ElMessage.info(`正在导出${format.toUpperCase()}格式...`)\n      // 这里可以调用导出API\n    }\n\n    // 打印教案\n    const handlePrint = () => {\n      window.print()\n    }\n\n    return {\n      formData,\n      objectiveInput,\n      methodOptions,\n      stepInput,\n      subjectOptions,\n      gradeOptions,\n      isGenerating,\n      showPreview,\n      addObjective,\n      removeObjective,\n      addStep,\n      removeStep,\n      generateLessonPlan,\n      resetForm,\n      previewLessonPlan,\n      handleExport,\n      handlePrint,\n      // 图标\n      Document,\n      Download,\n      Plus,\n      Delete,\n      Edit,\n      Loading\n    }\n  }\n}\n</script>\n\n<template>\n  <el-card class=\"lesson-plan-container\">\n    <template #header>\n      <div class=\"header\">\n        <span class=\"title\">\n          <el-icon><Document /></el-icon>\n          智能教案生成器\n        </span>\n        <div class=\"actions\">\n          <el-button type=\"info\" @click=\"previewLessonPlan\">\n            <el-icon><Edit /></el-icon>\n            预览\n          </el-button>\n          <el-button type=\"warning\" @click=\"resetForm\">\n            重置\n          </el-button>\n          <el-button\n            type=\"primary\"\n            @click=\"generateLessonPlan\"\n            :loading=\"isGenerating\"\n            :disabled=\"isGenerating\"\n          >\n            <el-icon v-if=\"!isGenerating\"><Download /></el-icon>\n            <el-icon v-else><Loading /></el-icon>\n            {{ isGenerating ? '生成中...' : '生成教案' }}\n          </el-button>\n        </div>\n      </div>\n    </template>\n\n    <el-form :model=\"formData\" label-width=\"120px\" class=\"lesson-form\">\n      <!-- 基本信息 -->\n      <el-card class=\"form-section\" shadow=\"never\">\n        <template #header>\n          <h3>基本信息</h3>\n        </template>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"学科\" required>\n              <el-select v-model=\"formData.subject\" placeholder=\"请选择学科\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"subject in subjectOptions\"\n                  :key=\"subject\"\n                  :label=\"subject\"\n                  :value=\"subject\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"年级\" required>\n              <el-select v-model=\"formData.grade\" placeholder=\"请选择年级\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"grade in gradeOptions\"\n                  :key=\"grade\"\n                  :label=\"grade\"\n                  :value=\"grade\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"课时长度\">\n              <el-input-number\n                v-model=\"formData.duration\"\n                :min=\"10\"\n                :max=\"120\"\n                :step=\"5\"\n                style=\"width: 100%\"\n              />\n              <span style=\"margin-left: 8px;\">分钟</span>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-form-item label=\"课题\" required>\n          <el-input\n            v-model=\"formData.topic\"\n            placeholder=\"请输入课题名称\"\n            maxlength=\"100\"\n            show-word-limit\n          />\n        </el-form-item>\n      </el-card>\n\n      <!-- 教学目标 -->\n      <el-card class=\"form-section\" shadow=\"never\">\n        <template #header>\n          <h3>教学目标</h3>\n        </template>\n\n        <div class=\"objective-input\">\n          <el-input\n            v-model=\"objectiveInput\"\n            placeholder=\"请输入教学目标\"\n            @keyup.enter=\"addObjective\"\n          >\n            <template #append>\n              <el-button @click=\"addObjective\" :icon=\"Plus\">添加</el-button>\n            </template>\n          </el-input>\n        </div>\n\n        <div class=\"objectives-list\" v-if=\"formData.objectives.length > 0\">\n          <el-tag\n            v-for=\"(objective, index) in formData.objectives\"\n            :key=\"index\"\n            closable\n            @close=\"removeObjective(index)\"\n            class=\"objective-tag\"\n          >\n            {{ objective }}\n          </el-tag>\n        </div>\n      </el-card>\n\n      <!-- 重点难点 -->\n      <el-card class=\"form-section\" shadow=\"never\">\n        <template #header>\n          <h3>重点难点</h3>\n        </template>\n\n        <el-form-item label=\"教学重点\">\n          <el-input\n            v-model=\"formData.keyPoints\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请描述本课的教学重点和难点\"\n            maxlength=\"500\"\n            show-word-limit\n          />\n        </el-form-item>\n      </el-card>\n\n      <!-- 教学方法 -->\n      <el-card class=\"form-section\" shadow=\"never\">\n        <template #header>\n          <h3>教学方法</h3>\n        </template>\n\n        <el-form-item label=\"教学方法\">\n          <el-checkbox-group v-model=\"formData.methods\">\n            <el-checkbox\n              v-for=\"method in methodOptions\"\n              :key=\"method.value\"\n              :label=\"method.value\"\n            >\n              {{ method.label }}\n            </el-checkbox>\n          </el-checkbox-group>\n        </el-form-item>\n\n        <el-form-item label=\"教学材料\">\n          <el-input\n            v-model=\"formData.materials\"\n            type=\"textarea\"\n            :rows=\"2\"\n            placeholder=\"请列出所需的教学材料和设备\"\n            maxlength=\"300\"\n            show-word-limit\n          />\n        </el-form-item>\n      </el-card>\n\n      <!-- 教学步骤 -->\n      <el-card class=\"form-section\" shadow=\"never\">\n        <template #header>\n          <h3>教学步骤</h3>\n        </template>\n\n        <div class=\"step-input\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-input\n                v-model=\"stepInput.title\"\n                placeholder=\"步骤标题\"\n                maxlength=\"50\"\n              />\n            </el-col>\n            <el-col :span=\"12\">\n              <el-input\n                v-model=\"stepInput.content\"\n                placeholder=\"步骤内容\"\n                maxlength=\"200\"\n              />\n            </el-col>\n            <el-col :span=\"4\">\n              <el-input-number\n                v-model=\"stepInput.time\"\n                :min=\"1\"\n                :max=\"60\"\n                placeholder=\"时长\"\n                style=\"width: 100%\"\n              />\n            </el-col>\n            <el-col :span=\"2\">\n              <el-button @click=\"addStep\" :icon=\"Plus\" type=\"primary\">添加</el-button>\n            </el-col>\n          </el-row>\n        </div>\n\n        <div class=\"steps-list\" v-if=\"formData.steps.length > 0\">\n          <el-timeline>\n            <el-timeline-item\n              v-for=\"(step, index) in formData.steps\"\n              :key=\"index\"\n              :timestamp=\"`${step.time}分钟`\"\n            >\n              <el-card class=\"step-card\">\n                <div class=\"step-header\">\n                  <h4>{{ step.title }}</h4>\n                  <el-button\n                    @click=\"removeStep(index)\"\n                    :icon=\"Delete\"\n                    size=\"small\"\n                    type=\"danger\"\n                    text\n                  />\n                </div>\n                <p>{{ step.content }}</p>\n              </el-card>\n            </el-timeline-item>\n          </el-timeline>\n        </div>\n      </el-card>\n\n      <!-- 作业与反思 -->\n      <el-card class=\"form-section\" shadow=\"never\">\n        <template #header>\n          <h3>作业与反思</h3>\n        </template>\n\n        <el-form-item label=\"作业布置\">\n          <el-input\n            v-model=\"formData.homework\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请描述课后作业安排\"\n            maxlength=\"300\"\n            show-word-limit\n          />\n        </el-form-item>\n\n        <el-form-item label=\"教学反思\">\n          <el-input\n            v-model=\"formData.reflection\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请填写教学反思（可在课后补充）\"\n            maxlength=\"500\"\n            show-word-limit\n          />\n        </el-form-item>\n      </el-card>\n    </el-form>\n\n    <!-- 教案预览组件 -->\n    <LessonPlanPreview\n      v-model:visible=\"showPreview\"\n      :lesson-plan=\"formData\"\n      @export=\"handleExport\"\n      @print=\"handlePrint\"\n    />\n  </el-card>\n</template>\n\n<style lang=\"scss\" scoped>\n.lesson-plan-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n  padding: 20px;\n\n  .header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n\n    .title {\n      font-size: 24px;\n      font-weight: bold;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      color: #409eff;\n    }\n\n    .actions {\n      display: flex;\n      gap: 10px;\n    }\n  }\n}\n\n.lesson-form {\n  .form-section {\n    margin-bottom: 20px;\n    border: 1px solid #e4e7ed;\n\n    h3 {\n      margin: 0;\n      color: #409eff;\n      font-size: 16px;\n    }\n  }\n\n  .objective-input {\n    margin-bottom: 15px;\n  }\n\n  .objectives-list {\n    .objective-tag {\n      margin: 5px 5px 5px 0;\n      padding: 8px 12px;\n      font-size: 14px;\n      background-color: #f0f9ff;\n      border-color: #409eff;\n    }\n  }\n\n  .step-input {\n    margin-bottom: 20px;\n    padding: 15px;\n    background-color: #f8f9fa;\n    border-radius: 6px;\n  }\n\n  .steps-list {\n    .step-card {\n      margin-bottom: 10px;\n\n      .step-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 10px;\n\n        h4 {\n          margin: 0;\n          color: #303133;\n          font-size: 14px;\n        }\n      }\n\n      p {\n        margin: 0;\n        color: #606266;\n        line-height: 1.6;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .lesson-plan-container {\n    margin: 10px;\n\n    .header {\n      flex-direction: column;\n      gap: 15px;\n\n      .actions {\n        width: 100%;\n        justify-content: center;\n      }\n    }\n  }\n\n  .step-input {\n    .el-row {\n      .el-col {\n        margin-bottom: 10px;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;EA8NWA,KAAK,EAAC;AAAQ;;EACXA,KAAK,EAAC;AAAO;;EAIdA,KAAK,EAAC;AAAS;;EAoFfA,KAAK,EAAC;AAAiB;;EAvTpCC,GAAA;EAmUaD,KAAK,EAAC;;;EAmENA,KAAK,EAAC;AAAY;;EAtY/BC,GAAA;EAqaaD,KAAK,EAAC;;;EAQEA,KAAK,EAAC;AAAa;;;;;;;;;;;;;;;;;;;;;;;uBAjNtCE,YAAA,CAuQUC,kBAAA;IAvQDH,KAAK,EAAC;EAAuB;IACzBI,MAAM,EAAAC,QAAA,CACf,MAwBM,CAxBNC,mBAAA,CAwBM,OAxBNC,UAwBM,GAvBJD,mBAAA,CAGO,QAHPE,UAGO,GAFLC,YAAA,CAA+BC,kBAAA;MAhOzCC,OAAA,EAAAN,QAAA,CAgOmB,MAAY,CAAZI,YAAA,CAAYG,mBAAA,E;MAhO/BC,CAAA;oCAAAC,gBAAA,CAgOyC,WAEjC,G,GACAR,mBAAA,CAkBM,OAlBNS,UAkBM,GAjBJN,YAAA,CAGYO,oBAAA;MAHDC,IAAI,EAAC,MAAM;MAAEC,OAAK,EAAEC,MAAA,CAAAC;;MApOzCT,OAAA,EAAAN,QAAA,CAqOY,MAA2B,CAA3BI,YAAA,CAA2BC,kBAAA;QArOvCC,OAAA,EAAAN,QAAA,CAqOqB,MAAQ,CAARI,YAAA,CAAQY,eAAA,E;QArO7BR,CAAA;sCAAAC,gBAAA,CAqOuC,MAE7B,G;MAvOVD,CAAA;oCAwOUJ,YAAA,CAEYO,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEC,MAAA,CAAAG;;MAxO5CX,OAAA,EAAAN,QAAA,CAwOuD,MAE7CkB,MAAA,SAAAA,MAAA,QA1OVT,gBAAA,CAwOuD,MAE7C,E;MA1OVD,CAAA;oCA2OUJ,YAAA,CASYO,oBAAA;MARVC,IAAI,EAAC,SAAS;MACbC,OAAK,EAAEC,MAAA,CAAAK,kBAAkB;MACzBC,OAAO,EAAEN,MAAA,CAAAO,YAAY;MACrBC,QAAQ,EAAER,MAAA,CAAAO;;MA/OvBf,OAAA,EAAAN,QAAA,CAiPY,MAAoD,C,CAApCc,MAAA,CAAAO,YAAY,I,cAA5BxB,YAAA,CAAoDQ,kBAAA;QAjPhET,GAAA;MAAA;QAAAU,OAAA,EAAAN,QAAA,CAiP0C,MAAY,CAAZI,YAAA,CAAYmB,mBAAA,E;QAjPtDf,CAAA;2BAkPYX,YAAA,CAAqCQ,kBAAA;QAlPjDT,GAAA;MAAA;QAAAU,OAAA,EAAAN,QAAA,CAkP4B,MAAW,CAAXI,YAAA,CAAWoB,kBAAA,E;QAlPvChB,CAAA;WAAAC,gBAAA,CAkPiD,GACrC,GAAAgB,gBAAA,CAAGX,MAAA,CAAAO,YAAY,qC;MAnP3Bb,CAAA;;IAAAF,OAAA,EAAAN,QAAA,CAyPI,MAiOU,CAjOVI,YAAA,CAiOUsB,kBAAA;MAjOAC,KAAK,EAAEb,MAAA,CAAAc,QAAQ;MAAE,aAAW,EAAC,OAAO;MAACjC,KAAK,EAAC;;MAzPzDW,OAAA,EAAAN,QAAA,CA0PM,MAAa,CAAb6B,mBAAA,UAAa,EACbzB,YAAA,CAoDUN,kBAAA;QApDDH,KAAK,EAAC,cAAc;QAACmC,MAAM,EAAC;;QACxB/B,MAAM,EAAAC,QAAA,CACf,MAAakB,MAAA,SAAAA,MAAA,QAAbjB,mBAAA,CAAa,YAAT,MAAI,oB;QA7PlBK,OAAA,EAAAN,QAAA,CAgQQ,MAqCS,CArCTI,YAAA,CAqCS2B,iBAAA;UArCAC,MAAM,EAAE;QAAE;UAhQ3B1B,OAAA,EAAAN,QAAA,CAiQU,MAWS,CAXTI,YAAA,CAWS6B,iBAAA;YAXAC,IAAI,EAAE;UAAC;YAjQ1B5B,OAAA,EAAAN,QAAA,CAkQY,MASe,CATfI,YAAA,CASe+B,uBAAA;cATDC,KAAK,EAAC,IAAI;cAACC,QAAQ,EAAR;;cAlQrC/B,OAAA,EAAAN,QAAA,CAmQc,MAOY,CAPZI,YAAA,CAOYkC,oBAAA;gBA1Q1BC,UAAA,EAmQkCzB,MAAA,CAAAc,QAAQ,CAACY,OAAO;gBAnQlD,uBAAAtB,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAmQkC3B,MAAA,CAAAc,QAAQ,CAACY,OAAO,GAAAC,MAAA;gBAAEC,WAAW,EAAC,OAAO;gBAACC,KAAmB,EAAnB;kBAAA;gBAAA;;gBAnQxErC,OAAA,EAAAN,QAAA,CAqQkB,MAAiC,E,kBADnC4C,mBAAA,CAKEC,SAAA,QAzQlBC,WAAA,CAqQoChC,MAAA,CAAAiC,cAAc,EAAzBP,OAAO;uCADhB3C,YAAA,CAKEmD,oBAAA;oBAHCpD,GAAG,EAAE4C,OAAO;oBACZJ,KAAK,EAAEI,OAAO;oBACdS,KAAK,EAAET;;;gBAxQ1BhC,CAAA;;cAAAA,CAAA;;YAAAA,CAAA;cA6QUJ,YAAA,CAWS6B,iBAAA;YAXAC,IAAI,EAAE;UAAC;YA7Q1B5B,OAAA,EAAAN,QAAA,CA8QY,MASe,CATfI,YAAA,CASe+B,uBAAA;cATDC,KAAK,EAAC,IAAI;cAACC,QAAQ,EAAR;;cA9QrC/B,OAAA,EAAAN,QAAA,CA+Qc,MAOY,CAPZI,YAAA,CAOYkC,oBAAA;gBAtR1BC,UAAA,EA+QkCzB,MAAA,CAAAc,QAAQ,CAACsB,KAAK;gBA/QhD,uBAAAhC,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IA+QkC3B,MAAA,CAAAc,QAAQ,CAACsB,KAAK,GAAAT,MAAA;gBAAEC,WAAW,EAAC,OAAO;gBAACC,KAAmB,EAAnB;kBAAA;gBAAA;;gBA/QtErC,OAAA,EAAAN,QAAA,CAiRkB,MAA6B,E,kBAD/B4C,mBAAA,CAKEC,SAAA,QArRlBC,WAAA,CAiRkChC,MAAA,CAAAqC,YAAY,EAArBD,KAAK;uCADdrD,YAAA,CAKEmD,oBAAA;oBAHCpD,GAAG,EAAEsD,KAAK;oBACVd,KAAK,EAAEc,KAAK;oBACZD,KAAK,EAAEC;;;gBApR1B1C,CAAA;;cAAAA,CAAA;;YAAAA,CAAA;cAyRUJ,YAAA,CAWS6B,iBAAA;YAXAC,IAAI,EAAE;UAAC;YAzR1B5B,OAAA,EAAAN,QAAA,CA0RY,MASe,CATfI,YAAA,CASe+B,uBAAA;cATDC,KAAK,EAAC;YAAM;cA1RtC9B,OAAA,EAAAN,QAAA,CA2Rc,MAME,CANFI,YAAA,CAMEgD,0BAAA;gBAjShBb,UAAA,EA4RyBzB,MAAA,CAAAc,QAAQ,CAACyB,QAAQ;gBA5R1C,uBAAAnC,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IA4RyB3B,MAAA,CAAAc,QAAQ,CAACyB,QAAQ,GAAAZ,MAAA;gBACzBa,GAAG,EAAE,EAAE;gBACPC,GAAG,EAAE,GAAG;gBACRC,IAAI,EAAE,CAAC;gBACRb,KAAmB,EAAnB;kBAAA;gBAAA;mFAEF1C,mBAAA,CAAyC;gBAAnC0C,KAAyB,EAAzB;kBAAA;gBAAA;cAAyB,GAAC,IAAE,qB;cAlShDnC,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;YAuSQJ,YAAA,CAOe+B,uBAAA;UAPDC,KAAK,EAAC,IAAI;UAACC,QAAQ,EAAR;;UAvSjC/B,OAAA,EAAAN,QAAA,CAwSU,MAKE,CALFI,YAAA,CAKEqD,mBAAA;YA7SZlB,UAAA,EAySqBzB,MAAA,CAAAc,QAAQ,CAAC8B,KAAK;YAzSnC,uBAAAxC,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAySqB3B,MAAA,CAAAc,QAAQ,CAAC8B,KAAK,GAAAjB,MAAA;YACvBC,WAAW,EAAC,SAAS;YACrBiB,SAAS,EAAC,KAAK;YACf,iBAAe,EAAf;;UA5SZnD,CAAA;;QAAAA,CAAA;UAiTMqB,mBAAA,UAAa,EACbzB,YAAA,CA4BUN,kBAAA;QA5BDH,KAAK,EAAC,cAAc;QAACmC,MAAM,EAAC;;QACxB/B,MAAM,EAAAC,QAAA,CACf,MAAakB,MAAA,SAAAA,MAAA,QAAbjB,mBAAA,CAAa,YAAT,MAAI,oB;QApTlBK,OAAA,EAAAN,QAAA,CAuTQ,MAUM,CAVNC,mBAAA,CAUM,OAVN2D,UAUM,GATJxD,YAAA,CAQWqD,mBAAA;UAhUrBlB,UAAA,EAyTqBzB,MAAA,CAAA+C,cAAc;UAzTnC,uBAAA3C,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAyTqB3B,MAAA,CAAA+C,cAAc,GAAApB,MAAA;UACvBC,WAAW,EAAC,SAAS;UACpBoB,OAAK,EA3TlBC,SAAA,CA2T0BjD,MAAA,CAAAkD,YAAY;;UAEfC,MAAM,EAAAjE,QAAA,CACf,MAA4D,CAA5DI,YAAA,CAA4DO,oBAAA;YAAhDE,OAAK,EAAEC,MAAA,CAAAkD,YAAY;YAAGE,IAAI,EAAEpD,MAAA,CAAAqD;;YA9TtD7D,OAAA,EAAAN,QAAA,CA8T4D,MAAEkB,MAAA,SAAAA,MAAA,QA9T9DT,gBAAA,CA8T4D,IAAE,E;YA9T9DD,CAAA;;UAAAA,CAAA;wDAmU2CM,MAAA,CAAAc,QAAQ,CAACwC,UAAU,CAACC,MAAM,Q,cAA7DzB,mBAAA,CAUM,OAVN0B,UAUM,I,kBATJ1B,mBAAA,CAQSC,SAAA,QA5UnBC,WAAA,CAqUyChC,MAAA,CAAAc,QAAQ,CAACwC,UAAU,EArU5D,CAqUoBG,SAAS,EAAEC,KAAK;+BAD1B3E,YAAA,CAQS4E,iBAAA;YANN7E,GAAG,EAAE4E,KAAK;YACXE,QAAQ,EAAR,EAAQ;YACPC,OAAK,EAAAlC,MAAA,IAAE3B,MAAA,CAAA8D,eAAe,CAACJ,KAAK;YAC7B7E,KAAK,EAAC;;YAzUlBW,OAAA,EAAAN,QAAA,CA2UY,MAAe,CA3U3BS,gBAAA,CAAAgB,gBAAA,CA2Ue8C,SAAS,iB;YA3UxB/D,CAAA;;4CAAAqB,mBAAA,e;QAAArB,CAAA;UAgVMqB,mBAAA,UAAa,EACbzB,YAAA,CAeUN,kBAAA;QAfDH,KAAK,EAAC,cAAc;QAACmC,MAAM,EAAC;;QACxB/B,MAAM,EAAAC,QAAA,CACf,MAAakB,MAAA,SAAAA,MAAA,QAAbjB,mBAAA,CAAa,YAAT,MAAI,oB;QAnVlBK,OAAA,EAAAN,QAAA,CAsVQ,MASe,CATfI,YAAA,CASe+B,uBAAA;UATDC,KAAK,EAAC;QAAM;UAtVlC9B,OAAA,EAAAN,QAAA,CAuVU,MAOE,CAPFI,YAAA,CAOEqD,mBAAA;YA9VZlB,UAAA,EAwVqBzB,MAAA,CAAAc,QAAQ,CAACiD,SAAS;YAxVvC,uBAAA3D,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAwVqB3B,MAAA,CAAAc,QAAQ,CAACiD,SAAS,GAAApC,MAAA;YAC3B7B,IAAI,EAAC,UAAU;YACdkE,IAAI,EAAE,CAAC;YACRpC,WAAW,EAAC,eAAe;YAC3BiB,SAAS,EAAC,KAAK;YACf,iBAAe,EAAf;;UA7VZnD,CAAA;;QAAAA,CAAA;UAkWMqB,mBAAA,UAAa,EACbzB,YAAA,CA2BUN,kBAAA;QA3BDH,KAAK,EAAC,cAAc;QAACmC,MAAM,EAAC;;QACxB/B,MAAM,EAAAC,QAAA,CACf,MAAakB,MAAA,SAAAA,MAAA,QAAbjB,mBAAA,CAAa,YAAT,MAAI,oB;QArWlBK,OAAA,EAAAN,QAAA,CAwWQ,MAUe,CAVfI,YAAA,CAUe+B,uBAAA;UAVDC,KAAK,EAAC;QAAM;UAxWlC9B,OAAA,EAAAN,QAAA,CAyWU,MAQoB,CARpBI,YAAA,CAQoB2E,4BAAA;YAjX9BxC,UAAA,EAyWsCzB,MAAA,CAAAc,QAAQ,CAACoD,OAAO;YAzWtD,uBAAA9D,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAyWsC3B,MAAA,CAAAc,QAAQ,CAACoD,OAAO,GAAAvC,MAAA;;YAzWtDnC,OAAA,EAAAN,QAAA,CA2Wc,MAA+B,E,kBADjC4C,mBAAA,CAMcC,SAAA,QAhX1BC,WAAA,CA2W+BhC,MAAA,CAAAmE,aAAa,EAAvBC,MAAM;mCADfrF,YAAA,CAMcsF,sBAAA;gBAJXvF,GAAG,EAAEsF,MAAM,CAACjC,KAAK;gBACjBb,KAAK,EAAE8C,MAAM,CAACjC;;gBA7W7B3C,OAAA,EAAAN,QAAA,CA+Wc,MAAkB,CA/WhCS,gBAAA,CAAAgB,gBAAA,CA+WiByD,MAAM,CAAC9C,KAAK,iB;gBA/W7B5B,CAAA;;;YAAAA,CAAA;;UAAAA,CAAA;YAoXQJ,YAAA,CASe+B,uBAAA;UATDC,KAAK,EAAC;QAAM;UApXlC9B,OAAA,EAAAN,QAAA,CAqXU,MAOE,CAPFI,YAAA,CAOEqD,mBAAA;YA5XZlB,UAAA,EAsXqBzB,MAAA,CAAAc,QAAQ,CAACwD,SAAS;YAtXvC,uBAAAlE,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAsXqB3B,MAAA,CAAAc,QAAQ,CAACwD,SAAS,GAAA3C,MAAA;YAC3B7B,IAAI,EAAC,UAAU;YACdkE,IAAI,EAAE,CAAC;YACRpC,WAAW,EAAC,eAAe;YAC3BiB,SAAS,EAAC,KAAK;YACf,iBAAe,EAAf;;UA3XZnD,CAAA;;QAAAA,CAAA;UAgYMqB,mBAAA,UAAa,EACbzB,YAAA,CA2DUN,kBAAA;QA3DDH,KAAK,EAAC,cAAc;QAACmC,MAAM,EAAC;;QACxB/B,MAAM,EAAAC,QAAA,CACf,MAAakB,MAAA,SAAAA,MAAA,QAAbjB,mBAAA,CAAa,YAAT,MAAI,oB;QAnYlBK,OAAA,EAAAN,QAAA,CAsYQ,MA6BM,CA7BNC,mBAAA,CA6BM,OA7BNoF,UA6BM,GA5BJjF,YAAA,CA2BS2B,iBAAA;UA3BAC,MAAM,EAAE;QAAE;UAvY7B1B,OAAA,EAAAN,QAAA,CAwYY,MAMS,CANTI,YAAA,CAMS6B,iBAAA;YANAC,IAAI,EAAE;UAAC;YAxY5B5B,OAAA,EAAAN,QAAA,CAyYc,MAIE,CAJFI,YAAA,CAIEqD,mBAAA;cA7YhBlB,UAAA,EA0YyBzB,MAAA,CAAAwE,SAAS,CAACC,KAAK;cA1YxC,uBAAArE,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IA0YyB3B,MAAA,CAAAwE,SAAS,CAACC,KAAK,GAAA9C,MAAA;cACxBC,WAAW,EAAC,MAAM;cAClBiB,SAAS,EAAC;;YA5Y1BnD,CAAA;cA+YYJ,YAAA,CAMS6B,iBAAA;YANAC,IAAI,EAAE;UAAE;YA/Y7B5B,OAAA,EAAAN,QAAA,CAgZc,MAIE,CAJFI,YAAA,CAIEqD,mBAAA;cApZhBlB,UAAA,EAiZyBzB,MAAA,CAAAwE,SAAS,CAACE,OAAO;cAjZ1C,uBAAAtE,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAiZyB3B,MAAA,CAAAwE,SAAS,CAACE,OAAO,GAAA/C,MAAA;cAC1BC,WAAW,EAAC,MAAM;cAClBiB,SAAS,EAAC;;YAnZ1BnD,CAAA;cAsZYJ,YAAA,CAQS6B,iBAAA;YARAC,IAAI,EAAE;UAAC;YAtZ5B5B,OAAA,EAAAN,QAAA,CAuZc,MAME,CANFI,YAAA,CAMEgD,0BAAA;cA7ZhBb,UAAA,EAwZyBzB,MAAA,CAAAwE,SAAS,CAACG,IAAI;cAxZvC,uBAAAvE,MAAA,SAAAA,MAAA,OAAAuB,MAAA,IAwZyB3B,MAAA,CAAAwE,SAAS,CAACG,IAAI,GAAAhD,MAAA;cACtBa,GAAG,EAAE,CAAC;cACNC,GAAG,EAAE,EAAE;cACRb,WAAW,EAAC,IAAI;cAChBC,KAAmB,EAAnB;gBAAA;cAAA;;YA5ZhBnC,CAAA;cA+ZYJ,YAAA,CAES6B,iBAAA;YAFAC,IAAI,EAAE;UAAC;YA/Z5B5B,OAAA,EAAAN,QAAA,CAgac,MAAsE,CAAtEI,YAAA,CAAsEO,oBAAA;cAA1DE,OAAK,EAAEC,MAAA,CAAA4E,OAAO;cAAGxB,IAAI,EAAEpD,MAAA,CAAAqD,IAAI;cAAEvD,IAAI,EAAC;;cAha5DN,OAAA,EAAAN,QAAA,CAgasE,MAAEkB,MAAA,SAAAA,MAAA,QAhaxET,gBAAA,CAgasE,IAAE,E;cAhaxED,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;cAqasCM,MAAA,CAAAc,QAAQ,CAAC+D,KAAK,CAACtB,MAAM,Q,cAAnDzB,mBAAA,CAsBM,OAtBNgD,UAsBM,GArBJxF,YAAA,CAoBcyF,sBAAA;UA1bxBvF,OAAA,EAAAN,QAAA,CAwac,MAAuC,E,kBADzC4C,mBAAA,CAkBmBC,SAAA,QAzb/BC,WAAA,CAwasChC,MAAA,CAAAc,QAAQ,CAAC+D,KAAK,EAxapD,CAwasBnC,IAAI,EAAEgB,KAAK;iCADrB3E,YAAA,CAkBmBiG,2BAAA;cAhBhBlG,GAAG,EAAE4E,KAAK;cACVuB,SAAS,KAAKvC,IAAI,CAACiC,IAAI;;cA1atCnF,OAAA,EAAAN,QAAA,CA4ac,MAYU,CAZVI,YAAA,CAYUN,kBAAA;gBAZDH,KAAK,EAAC;cAAW;gBA5axCW,OAAA,EAAAN,QAAA,CA6agB,MASM,CATNC,mBAAA,CASM,OATN+F,UASM,GARJ/F,mBAAA,CAAyB,YAAAwB,gBAAA,CAAlB+B,IAAI,CAAC+B,KAAK,kBACjBnF,YAAA,CAMEO,oBAAA;kBALCE,OAAK,EAAA4B,MAAA,IAAE3B,MAAA,CAAAmF,UAAU,CAACzB,KAAK;kBACvBN,IAAI,EAAEpD,MAAA,CAAAoF,MAAM;kBACbC,IAAI,EAAC,OAAO;kBACZvF,IAAI,EAAC,QAAQ;kBACbwF,IAAI,EAAJ;gEAGJnG,mBAAA,CAAyB,WAAAwB,gBAAA,CAAnB+B,IAAI,CAACgC,OAAO,iB;gBAvblChF,CAAA;;cAAAA,CAAA;;;UAAAA,CAAA;gBAAAqB,mBAAA,e;QAAArB,CAAA;UA8bMqB,mBAAA,WAAc,EACdzB,YAAA,CA0BUN,kBAAA;QA1BDH,KAAK,EAAC,cAAc;QAACmC,MAAM,EAAC;;QACxB/B,MAAM,EAAAC,QAAA,CACf,MAAckB,MAAA,SAAAA,MAAA,QAAdjB,mBAAA,CAAc,YAAV,OAAK,oB;QAjcnBK,OAAA,EAAAN,QAAA,CAocQ,MASe,CATfI,YAAA,CASe+B,uBAAA;UATDC,KAAK,EAAC;QAAM;UApclC9B,OAAA,EAAAN,QAAA,CAqcU,MAOE,CAPFI,YAAA,CAOEqD,mBAAA;YA5cZlB,UAAA,EAscqBzB,MAAA,CAAAc,QAAQ,CAACyE,QAAQ;YAtctC,uBAAAnF,MAAA,SAAAA,MAAA,OAAAuB,MAAA,IAscqB3B,MAAA,CAAAc,QAAQ,CAACyE,QAAQ,GAAA5D,MAAA;YAC1B7B,IAAI,EAAC,UAAU;YACdkE,IAAI,EAAE,CAAC;YACRpC,WAAW,EAAC,WAAW;YACvBiB,SAAS,EAAC,KAAK;YACf,iBAAe,EAAf;;UA3cZnD,CAAA;YA+cQJ,YAAA,CASe+B,uBAAA;UATDC,KAAK,EAAC;QAAM;UA/clC9B,OAAA,EAAAN,QAAA,CAgdU,MAOE,CAPFI,YAAA,CAOEqD,mBAAA;YAvdZlB,UAAA,EAidqBzB,MAAA,CAAAc,QAAQ,CAAC0E,UAAU;YAjdxC,uBAAApF,MAAA,SAAAA,MAAA,OAAAuB,MAAA,IAidqB3B,MAAA,CAAAc,QAAQ,CAAC0E,UAAU,GAAA7D,MAAA;YAC5B7B,IAAI,EAAC,UAAU;YACdkE,IAAI,EAAE,CAAC;YACRpC,WAAW,EAAC,iBAAiB;YAC7BiB,SAAS,EAAC,KAAK;YACf,iBAAe,EAAf;;UAtdZnD,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;kCA6dIJ,YAAA,CAKEmG,4BAAA;MAJQC,OAAO,EAAE1F,MAAA,CAAA2F,WAAW;MA9dlC,oBAAAvF,MAAA,SAAAA,MAAA,OAAAuB,MAAA,IA8duB3B,MAAA,CAAA2F,WAAW,GAAAhE,MAAA;MAC3B,aAAW,EAAE3B,MAAA,CAAAc,QAAQ;MACrB8E,QAAM,EAAE5F,MAAA,CAAA6F,YAAY;MACpBC,OAAK,EAAE9F,MAAA,CAAA+F;;IAjedrG,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}