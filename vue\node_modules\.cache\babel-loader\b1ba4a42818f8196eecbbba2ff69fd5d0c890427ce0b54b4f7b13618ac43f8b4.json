{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { computed } from 'vue';\nimport { Document, Download, Print } from '@element-plus/icons-vue';\nexport default {\n  name: 'LessonPlanPreview',\n  props: {\n    lessonPlan: {\n      type: Object,\n      required: true\n    },\n    visible: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['update:visible', 'export', 'print'],\n  setup(props, {\n    emit\n  }) {\n    // 教学方法映射\n    const methodLabels = {\n      lecture: '讲授法',\n      discussion: '讨论法',\n      experiment: '实验法',\n      demonstration: '演示法',\n      practice: '练习法',\n      case_study: '案例分析法',\n      group_work: '小组合作',\n      multimedia: '多媒体教学'\n    };\n\n    // 计算总时长\n    const totalDuration = computed(() => {\n      return props.lessonPlan.steps?.reduce((total, step) => total + step.time, 0) || 0;\n    });\n\n    // 关闭对话框\n    const handleClose = () => {\n      emit('update:visible', false);\n    };\n\n    // 导出教案\n    const handleExport = format => {\n      emit('export', format);\n    };\n\n    // 打印教案\n    const handlePrint = () => {\n      emit('print');\n    };\n\n    // 获取教学方法标签\n    const getMethodLabel = method => {\n      return methodLabels[method] || method;\n    };\n    return {\n      totalDuration,\n      handleClose,\n      handleExport,\n      handlePrint,\n      getMethodLabel,\n      Document,\n      Download,\n      Print\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "Document", "Download", "Print", "name", "props", "lessonPlan", "type", "Object", "required", "visible", "Boolean", "default", "emits", "setup", "emit", "methodLabels", "lecture", "discussion", "experiment", "demonstration", "practice", "case_study", "group_work", "multimedia", "totalDuration", "steps", "reduce", "total", "step", "time", "handleClose", "handleExport", "format", "handlePrint", "getMethodLabel", "method"], "sources": ["C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\LessonPlanPreview.vue"], "sourcesContent": ["<script>\nimport { computed } from 'vue'\nimport { Document, Download, Print } from '@element-plus/icons-vue'\n\nexport default {\n  name: 'LessonPlanPreview',\n  props: {\n    lessonPlan: {\n      type: Object,\n      required: true\n    },\n    visible: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['update:visible', 'export', 'print'],\n  setup(props, { emit }) {\n    // 教学方法映射\n    const methodLabels = {\n      lecture: '讲授法',\n      discussion: '讨论法',\n      experiment: '实验法',\n      demonstration: '演示法',\n      practice: '练习法',\n      case_study: '案例分析法',\n      group_work: '小组合作',\n      multimedia: '多媒体教学'\n    }\n\n    // 计算总时长\n    const totalDuration = computed(() => {\n      return props.lessonPlan.steps?.reduce((total, step) => total + step.time, 0) || 0\n    })\n\n    // 关闭对话框\n    const handleClose = () => {\n      emit('update:visible', false)\n    }\n\n    // 导出教案\n    const handleExport = (format) => {\n      emit('export', format)\n    }\n\n    // 打印教案\n    const handlePrint = () => {\n      emit('print')\n    }\n\n    // 获取教学方法标签\n    const getMethodLabel = (method) => {\n      return methodLabels[method] || method\n    }\n\n    return {\n      totalDuration,\n      handleClose,\n      handleExport,\n      handlePrint,\n      getMethodLabel,\n      Document,\n      Download,\n      Print\n    }\n  }\n}\n</script>\n\n<template>\n  <el-dialog\n    :model-value=\"visible\"\n    @update:model-value=\"handleClose\"\n    title=\"教案预览\"\n    width=\"80%\"\n    class=\"lesson-preview-dialog\"\n  >\n    <div class=\"lesson-preview\">\n      <!-- 教案标题 -->\n      <div class=\"lesson-header\">\n        <h1>{{ lessonPlan.topic }}</h1>\n        <div class=\"lesson-meta\">\n          <span>学科：{{ lessonPlan.subject }}</span>\n          <span>年级：{{ lessonPlan.grade }}</span>\n          <span>课时：{{ lessonPlan.duration }}分钟</span>\n        </div>\n      </div>\n\n      <!-- 教学目标 -->\n      <div class=\"lesson-section\" v-if=\"lessonPlan.objectives?.length\">\n        <h3>教学目标</h3>\n        <ul>\n          <li v-for=\"(objective, index) in lessonPlan.objectives\" :key=\"index\">\n            {{ objective }}\n          </li>\n        </ul>\n      </div>\n\n      <!-- 重点难点 -->\n      <div class=\"lesson-section\" v-if=\"lessonPlan.keyPoints\">\n        <h3>重点难点</h3>\n        <p>{{ lessonPlan.keyPoints }}</p>\n      </div>\n\n      <!-- 教学方法 -->\n      <div class=\"lesson-section\" v-if=\"lessonPlan.methods?.length\">\n        <h3>教学方法</h3>\n        <div class=\"methods-list\">\n          <el-tag v-for=\"method in lessonPlan.methods\" :key=\"method\" class=\"method-tag\">\n            {{ getMethodLabel(method) }}\n          </el-tag>\n        </div>\n      </div>\n\n      <!-- 教学材料 -->\n      <div class=\"lesson-section\" v-if=\"lessonPlan.materials\">\n        <h3>教学材料</h3>\n        <p>{{ lessonPlan.materials }}</p>\n      </div>\n\n      <!-- 教学步骤 -->\n      <div class=\"lesson-section\" v-if=\"lessonPlan.steps?.length\">\n        <h3>教学步骤（总计：{{ totalDuration }}分钟）</h3>\n        <div class=\"steps-timeline\">\n          <div v-for=\"(step, index) in lessonPlan.steps\" :key=\"index\" class=\"step-item\">\n            <div class=\"step-number\">{{ index + 1 }}</div>\n            <div class=\"step-content\">\n              <h4>{{ step.title }} <span class=\"step-time\">（{{ step.time }}分钟）</span></h4>\n              <p>{{ step.content }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 作业布置 -->\n      <div class=\"lesson-section\" v-if=\"lessonPlan.homework\">\n        <h3>作业布置</h3>\n        <p>{{ lessonPlan.homework }}</p>\n      </div>\n\n      <!-- 教学反思 -->\n      <div class=\"lesson-section\" v-if=\"lessonPlan.reflection\">\n        <h3>教学反思</h3>\n        <p>{{ lessonPlan.reflection }}</p>\n      </div>\n    </div>\n\n    <template #footer>\n      <div class=\"dialog-footer\">\n        <el-button @click=\"handlePrint\" :icon=\"Print\">打印</el-button>\n        <el-button @click=\"handleExport('pdf')\" :icon=\"Download\">导出PDF</el-button>\n        <el-button @click=\"handleExport('word')\" :icon=\"Document\">导出Word</el-button>\n        <el-button type=\"primary\" @click=\"handleClose\">关闭</el-button>\n      </div>\n    </template>\n  </el-dialog>\n</template>\n\n<style lang=\"scss\" scoped>\n.lesson-preview-dialog {\n  .lesson-preview {\n    max-height: 70vh;\n    overflow-y: auto;\n    padding: 20px;\n\n    .lesson-header {\n      text-align: center;\n      margin-bottom: 30px;\n      border-bottom: 2px solid #409eff;\n      padding-bottom: 20px;\n\n      h1 {\n        margin: 0 0 15px 0;\n        color: #303133;\n        font-size: 28px;\n      }\n\n      .lesson-meta {\n        display: flex;\n        justify-content: center;\n        gap: 30px;\n        color: #606266;\n        font-size: 16px;\n      }\n    }\n\n    .lesson-section {\n      margin-bottom: 25px;\n\n      h3 {\n        color: #409eff;\n        margin-bottom: 15px;\n        font-size: 18px;\n        border-left: 4px solid #409eff;\n        padding-left: 10px;\n      }\n\n      p {\n        line-height: 1.8;\n        color: #606266;\n        margin: 0;\n      }\n\n      ul {\n        margin: 0;\n        padding-left: 20px;\n\n        li {\n          line-height: 1.8;\n          color: #606266;\n          margin-bottom: 8px;\n        }\n      }\n\n      .methods-list {\n        .method-tag {\n          margin: 5px 5px 5px 0;\n          background-color: #f0f9ff;\n          border-color: #409eff;\n        }\n      }\n\n      .steps-timeline {\n        .step-item {\n          display: flex;\n          margin-bottom: 20px;\n\n          .step-number {\n            width: 30px;\n            height: 30px;\n            background-color: #409eff;\n            color: white;\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            font-weight: bold;\n            margin-right: 15px;\n            flex-shrink: 0;\n          }\n\n          .step-content {\n            flex: 1;\n\n            h4 {\n              margin: 0 0 10px 0;\n              color: #303133;\n\n              .step-time {\n                color: #909399;\n                font-weight: normal;\n                font-size: 14px;\n              }\n            }\n\n            p {\n              margin: 0;\n              line-height: 1.6;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n}\n\n@media print {\n  .lesson-preview {\n    .lesson-header {\n      border-bottom: 2px solid #000;\n    }\n\n    .lesson-section h3 {\n      border-left: 4px solid #000;\n    }\n  }\n}\n</style>\n"], "mappings": ";;AACA,SAASA,QAAO,QAAS,KAAI;AAC7B,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,KAAI,QAAS,yBAAwB;AAElE,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE;IACLC,UAAU,EAAE;MACVC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,OAAO,EAAE;MACPH,IAAI,EAAEI,OAAO;MACbC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,KAAK,EAAE,CAAC,gBAAgB,EAAE,QAAQ,EAAE,OAAO,CAAC;EAC5CC,KAAKA,CAACT,KAAK,EAAE;IAAEU;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,YAAW,GAAI;MACnBC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE,KAAK;MACjBC,aAAa,EAAE,KAAK;MACpBC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE,OAAO;MACnBC,UAAU,EAAE,MAAM;MAClBC,UAAU,EAAE;IACd;;IAEA;IACA,MAAMC,aAAY,GAAIzB,QAAQ,CAAC,MAAM;MACnC,OAAOK,KAAK,CAACC,UAAU,CAACoB,KAAK,EAAEC,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAI,GAAIC,IAAI,CAACC,IAAI,EAAE,CAAC,KAAK;IAClF,CAAC;;IAED;IACA,MAAMC,WAAU,GAAIA,CAAA,KAAM;MACxBhB,IAAI,CAAC,gBAAgB,EAAE,KAAK;IAC9B;;IAEA;IACA,MAAMiB,YAAW,GAAKC,MAAM,IAAK;MAC/BlB,IAAI,CAAC,QAAQ,EAAEkB,MAAM;IACvB;;IAEA;IACA,MAAMC,WAAU,GAAIA,CAAA,KAAM;MACxBnB,IAAI,CAAC,OAAO;IACd;;IAEA;IACA,MAAMoB,cAAa,GAAKC,MAAM,IAAK;MACjC,OAAOpB,YAAY,CAACoB,MAAM,KAAKA,MAAK;IACtC;IAEA,OAAO;MACLX,aAAa;MACbM,WAAW;MACXC,YAAY;MACZE,WAAW;MACXC,cAAc;MACdlC,QAAQ;MACRC,QAAQ;MACRC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}