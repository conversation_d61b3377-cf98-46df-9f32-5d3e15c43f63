{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { Management, Promotion, UserFilled, User, Crop, EditPen, SwitchButton, CaretBottom, Document } from '@element-plus/icons-vue';\nimport avatar from '@/assets/default.png';\nimport { userInfoService } from '@/api/user.js';\nimport useUserInfoStore from '@/stores/userInfo.js';\nimport { useTokenStore } from '@/stores/token.js';\nimport { useRouter } from 'vue-router';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nexport default {\n  name: 'LayoutView',\n  setup() {\n    const tokenStore = useTokenStore();\n    const userInfoStore = useUserInfoStore();\n    const router = useRouter();\n\n    //调用函数,获取用户详细信息\n    const getUserInfo = async () => {\n      //调用接口\n      let result = await userInfoService();\n      //数据存储到pinia中\n      userInfoStore.setInfo(result.data);\n    };\n    getUserInfo();\n\n    //条目被点击后,调用的函数\n    const handleCommand = command => {\n      //判断指令\n      if (command === 'logout') {\n        //退出登录\n        ElMessageBox.confirm('您确认要退出吗?', '温馨提示', {\n          confirmButtonText: '确认',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(async () => {\n          //退出登录\n          //1.清空pinia中存储的token以及个人信息\n          tokenStore.removeToken();\n          userInfoStore.removeInfo();\n\n          //2.跳转到登录页面\n          router.push('/login');\n          ElMessage({\n            type: 'success',\n            message: '退出登录成功'\n          });\n        }).catch(() => {\n          ElMessage({\n            type: 'info',\n            message: '用户取消了退出登录'\n          });\n        });\n      } else {\n        //路由\n        router.push('/user/' + command);\n      }\n    };\n    return {\n      userInfoStore,\n      avatar,\n      handleCommand,\n      // 图标组件\n      Management,\n      Promotion,\n      UserFilled,\n      User,\n      Crop,\n      EditPen,\n      SwitchButton,\n      CaretBottom,\n      Document\n    };\n  }\n};", "map": {"version": 3, "names": ["Management", "Promotion", "UserFilled", "User", "Crop", "EditPen", "SwitchButton", "CaretBottom", "Document", "avatar", "userInfoService", "useUserInfoStore", "useTokenStore", "useRouter", "ElMessage", "ElMessageBox", "name", "setup", "tokenStore", "userInfoStore", "router", "getUserInfo", "result", "setInfo", "data", "handleCommand", "command", "confirm", "confirmButtonText", "cancelButtonText", "type", "then", "removeToken", "removeInfo", "push", "message", "catch"], "sources": ["C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\Layout.vue"], "sourcesContent": ["<script>\r\nimport {\r\n    Management,\r\n    Promotion,\r\n    UserFilled,\r\n    User,\r\n    Crop,\r\n    EditPen,\r\n    SwitchButton,\r\n    CaretBottom,\r\n    Document\r\n} from '@element-plus/icons-vue'\r\nimport avatar from '@/assets/default.png'\r\nimport {userInfoService} from '@/api/user.js'\r\nimport useUserInfoStore from '@/stores/userInfo.js'\r\nimport {useTokenStore} from '@/stores/token.js'\r\nimport {useRouter} from 'vue-router'\r\nimport {ElMessage,ElMessageBox} from 'element-plus'\r\n\r\nexport default {\r\n  name: 'LayoutView',\r\n  setup() {\r\n    const tokenStore = useTokenStore();\r\n    const userInfoStore = useUserInfoStore();\r\n    const router = useRouter();\r\n\r\n    //调用函数,获取用户详细信息\r\n    const getUserInfo = async() => {\r\n      //调用接口\r\n      let result = await userInfoService();\r\n      //数据存储到pinia中\r\n      userInfoStore.setInfo(result.data);\r\n    }\r\n\r\n    getUserInfo();\r\n\r\n    //条目被点击后,调用的函数\r\n    const handleCommand = (command) => {\r\n      //判断指令\r\n      if(command === 'logout'){\r\n        //退出登录\r\n        ElMessageBox.confirm(\r\n          '您确认要退出吗?',\r\n          '温馨提示',\r\n          {\r\n            confirmButtonText: '确认',\r\n            cancelButtonText: '取消',\r\n            type: 'warning',\r\n          }\r\n        )\r\n        .then(async () => {\r\n          //退出登录\r\n          //1.清空pinia中存储的token以及个人信息\r\n          tokenStore.removeToken()\r\n          userInfoStore.removeInfo()\r\n\r\n          //2.跳转到登录页面\r\n          router.push('/login')\r\n          ElMessage({\r\n            type: 'success',\r\n            message: '退出登录成功',\r\n          })\r\n\r\n        })\r\n        .catch(() => {\r\n          ElMessage({\r\n            type: 'info',\r\n            message: '用户取消了退出登录',\r\n          })\r\n        })\r\n      } else {\r\n        //路由\r\n        router.push('/user/'+command)\r\n      }\r\n    }\r\n\r\n    return {\r\n      userInfoStore,\r\n      avatar,\r\n      handleCommand,\r\n      // 图标组件\r\n      Management,\r\n      Promotion,\r\n      UserFilled,\r\n      User,\r\n      Crop,\r\n      EditPen,\r\n      SwitchButton,\r\n      CaretBottom,\r\n      Document\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n    <!-- element-plus中的容器 -->\r\n    <el-container class=\"layout-container\">\r\n        <!-- 左侧菜单 -->\r\n        <el-aside width=\"200px\">\r\n            <div class=\"el-aside__logo\"></div>\r\n            <!-- element-plus的菜单标签 -->\r\n            <el-menu active-text-color=\"#ffd04b\" background-color=\"#232323\"  text-color=\"#fff\"\r\n                router>\r\n                <el-menu-item index=\"/article/category\">\r\n                    <el-icon>\r\n                        <Management />\r\n                    </el-icon>\r\n                    <span>学习助手</span>\r\n                </el-menu-item>\r\n                <el-menu-item index=\"/article/manage\">\r\n                    <el-icon>\r\n                        <Promotion />\r\n                    </el-icon>\r\n                    <span>教案管理</span>\r\n                </el-menu-item>\r\n                <el-menu-item index=\"/lesson/generator\">\r\n                    <el-icon>\r\n                        <Document />\r\n                    </el-icon>\r\n                    <span>教案生成器</span>\r\n                </el-menu-item>\r\n                <el-sub-menu >\r\n                    <template #title>\r\n                        <el-icon>\r\n                            <UserFilled />\r\n                        </el-icon>\r\n                        <span>个人中心</span>\r\n                    </template>\r\n                    <el-menu-item index=\"/user/info\">\r\n                        <el-icon>\r\n                            <User />\r\n                        </el-icon>\r\n                        <span>基本资料</span>\r\n                    </el-menu-item>\r\n                    <el-menu-item index=\"/user/avatar\">\r\n                        <el-icon>\r\n                            <Crop />\r\n                        </el-icon>\r\n                        <span>更换头像</span>\r\n                    </el-menu-item>\r\n                    <el-menu-item index=\"/user/resetPassword\">\r\n                        <el-icon>\r\n                            <EditPen />\r\n                        </el-icon>\r\n                        <span>重置密码</span>\r\n                    </el-menu-item>\r\n                </el-sub-menu>\r\n            </el-menu>\r\n        </el-aside>\r\n        <!-- 右侧主区域 -->\r\n        <el-container>\r\n            <!-- 头部区域 -->\r\n            <el-header>\r\n                <div>身份：<strong>{{ userInfoStore.info.nickname }}</strong></div>\r\n                <!-- 下拉菜单 -->\r\n                <!-- command: 条目被点击后会触发,在事件函数上可以声明一个参数,接收条目对应的指令 -->\r\n                <el-dropdown placement=\"bottom-end\" @command=\"handleCommand\">\r\n                    <span class=\"el-dropdown__box\">\r\n                        <el-avatar :src=\"userInfoStore.info.userPic? userInfoStore.info.userPic:avatar\" />\r\n                        <el-icon>\r\n                            <CaretBottom />\r\n                        </el-icon>\r\n                    </span>\r\n                    <template #dropdown>\r\n                        <el-dropdown-menu>\r\n                            <el-dropdown-item command=\"info\" :icon=\"User\">基本资料</el-dropdown-item>\r\n                            <el-dropdown-item command=\"avatar\" :icon=\"Crop\">更换头像</el-dropdown-item>\r\n                            <el-dropdown-item command=\"resetPassword\" :icon=\"EditPen\">重置密码</el-dropdown-item>\r\n                            <el-dropdown-item command=\"logout\" :icon=\"SwitchButton\">退出登录</el-dropdown-item>\r\n                        </el-dropdown-menu>\r\n                    </template>\r\n                </el-dropdown>\r\n            </el-header>\r\n            <!-- 中间区域 -->\r\n            <el-main>\r\n                <!-- <div style=\"width: 1290px; height: 570px;border: 1px solid red;\">\r\n                    内容展示区\r\n                </div> -->\r\n                <router-view></router-view>\r\n            </el-main>\r\n            <!-- 底部区域 -->\r\n            <el-footer>师说 ©2025 Created by 师说开发团队</el-footer>\r\n        </el-container>\r\n    </el-container>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout-container {\r\n    height: 100vh;\r\n\r\n    .el-aside {\r\n        background-color: #232323;\r\n\r\n        &__logo {\r\n            height: 120px;\r\n            background: url('@/assets/sslogo.png') no-repeat center / 120px auto;\r\n        }\r\n\r\n        .el-menu {\r\n            border-right: none;\r\n        }\r\n    }\r\n\r\n    .el-header {\r\n        background-color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n\r\n        .el-dropdown__box {\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            .el-icon {\r\n                color: #999;\r\n                margin-left: 10px;\r\n            }\r\n\r\n            &:active,\r\n            &:focus {\r\n                outline: none;\r\n            }\r\n        }\r\n    }\r\n\r\n    .el-footer {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: 14px;\r\n        color: #666;\r\n    }\r\n}\r\n</style>"], "mappings": ";AACA,SACIA,UAAU,EACVC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,OAAO,EACPC,YAAY,EACZC,WAAW,EACXC,QAAO,QACJ,yBAAwB;AAC/B,OAAOC,MAAK,MAAO,sBAAqB;AACxC,SAAQC,eAAe,QAAO,eAAc;AAC5C,OAAOC,gBAAe,MAAO,sBAAqB;AAClD,SAAQC,aAAa,QAAO,mBAAkB;AAC9C,SAAQC,SAAS,QAAO,YAAW;AACnC,SAAQC,SAAS,EAACC,YAAY,QAAO,cAAa;AAElD,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,KAAKA,CAAA,EAAG;IACN,MAAMC,UAAS,GAAIN,aAAa,CAAC,CAAC;IAClC,MAAMO,aAAY,GAAIR,gBAAgB,CAAC,CAAC;IACxC,MAAMS,MAAK,GAAIP,SAAS,CAAC,CAAC;;IAE1B;IACA,MAAMQ,WAAU,GAAI,MAAAA,CAAA,KAAW;MAC7B;MACA,IAAIC,MAAK,GAAI,MAAMZ,eAAe,CAAC,CAAC;MACpC;MACAS,aAAa,CAACI,OAAO,CAACD,MAAM,CAACE,IAAI,CAAC;IACpC;IAEAH,WAAW,CAAC,CAAC;;IAEb;IACA,MAAMI,aAAY,GAAKC,OAAO,IAAK;MACjC;MACA,IAAGA,OAAM,KAAM,QAAQ,EAAC;QACtB;QACAX,YAAY,CAACY,OAAO,CAClB,UAAU,EACV,MAAM,EACN;UACEC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBC,IAAI,EAAE;QACR,CACF,EACCC,IAAI,CAAC,YAAY;UAChB;UACA;UACAb,UAAU,CAACc,WAAW,CAAC;UACvBb,aAAa,CAACc,UAAU,CAAC;;UAEzB;UACAb,MAAM,CAACc,IAAI,CAAC,QAAQ;UACpBpB,SAAS,CAAC;YACRgB,IAAI,EAAE,SAAS;YACfK,OAAO,EAAE;UACX,CAAC;QAEH,CAAC,EACAC,KAAK,CAAC,MAAM;UACXtB,SAAS,CAAC;YACRgB,IAAI,EAAE,MAAM;YACZK,OAAO,EAAE;UACX,CAAC;QACH,CAAC;MACH,OAAO;QACL;QACAf,MAAM,CAACc,IAAI,CAAC,QAAQ,GAACR,OAAO;MAC9B;IACF;IAEA,OAAO;MACLP,aAAa;MACbV,MAAM;MACNgB,aAAa;MACb;MACAzB,UAAU;MACVC,SAAS;MACTC,UAAU;MACVC,IAAI;MACJC,IAAI;MACJC,OAAO;MACPC,YAAY;MACZC,WAAW;MACXC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}