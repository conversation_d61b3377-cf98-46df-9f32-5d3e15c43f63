{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"home-container\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_FeatureCards = _resolveComponent(\"FeatureCards\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_FeatureCards)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_FeatureCards"], "sources": ["C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\home.vue"], "sourcesContent": ["<script>\r\nimport FeatureCards from './FeatureCards.vue'\r\n\r\nexport default {\r\n  name: 'HomePage',\r\n  components: {\r\n    FeatureCards\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"home-container\">\r\n    <FeatureCards />\r\n  </div>\r\n</template>\r\n\r\n<style scoped>\r\n.home-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n}\r\n</style>\r\n"], "mappings": ";;EAYOA,KAAK,EAAC;AAAgB;;;uBAA3BC,mBAAA,CAEM,OAFNC,UAEM,GADJC,YAAA,CAAgBC,uBAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}