{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"home\"\n};\nconst _hoisted_2 = {\n  class: \"button-container\"\n};\nconst _hoisted_3 = {\n  class: \"icon-container\"\n};\nconst _hoisted_4 = {\n  class: \"icon-container\"\n};\nconst _hoisted_5 = {\n  class: \"icon-container\"\n};\nconst _hoisted_6 = {\n  class: \"icon-container\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_ChatBubbleLeftRightIcon = _resolveComponent(\"ChatBubbleLeftRightIcon\");\n  const _component_PresentationChartBarIcon = _resolveComponent(\"PresentationChartBarIcon\");\n  const _component_DocumentCheckIcon = _resolveComponent(\"DocumentCheckIcon\");\n  const _component_AcademicCapIcon = _resolveComponent(\"AcademicCapIcon\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n    class: \"header\"\n  }, [_createElementVNode(\"h1\", null, \"师说教学辅助系统\"), _createElementVNode(\"p\", {\n    class: \"subtitle\"\n  }, \"智能教学助手，助力教育创新\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", {\n    class: \"feature-card\",\n    onClick: _cache[0] || (_cache[0] = $event => $options.navigateToPage('/aichat'))\n  }, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_ChatBubbleLeftRightIcon, {\n    class: \"icon\"\n  })]), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n    class: \"content\"\n  }, [_createElementVNode(\"h3\", null, \"知识问答\"), _createElementVNode(\"p\", null, \"智能解答教学相关问题，提供专业指导\")], -1 /* HOISTED */))]), _createElementVNode(\"div\", {\n    class: \"feature-card\",\n    onClick: _cache[1] || (_cache[1] = $event => $options.navigateToPage('/pptcreate'))\n  }, [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_PresentationChartBarIcon, {\n    class: \"icon\"\n  })]), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"content\"\n  }, [_createElementVNode(\"h3\", null, \"PPT生成\"), _createElementVNode(\"p\", null, \"快速生成教学课件，提升备课效率\")], -1 /* HOISTED */))]), _createElementVNode(\"div\", {\n    class: \"feature-card\",\n    onClick: _cache[2] || (_cache[2] = $event => $options.navigateToPage('/aichat'))\n  }, [_createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_DocumentCheckIcon, {\n    class: \"icon\"\n  }), _cache[6] || (_cache[6] = _createTextVNode(\"jio \"))]), _cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n    class: \"content\"\n  }, [_createElementVNode(\"h3\", null, \"作业批改\"), _createElementVNode(\"p\", null, \"智能批改学生作业，提供详细反馈\")], -1 /* HOISTED */))]), _createElementVNode(\"div\", {\n    class: \"feature-card\",\n    onClick: _cache[3] || (_cache[3] = $event => $options.navigateToPage('/jiaoan'))\n  }, [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_AcademicCapIcon, {\n    class: \"icon\"\n  })]), _cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n    class: \"content\"\n  }, [_createElementVNode(\"h3\", null, \"智能教案生成\"), _createElementVNode(\"p\", null, \"AI驱动的教案生成工具，快速创建完整教案\")], -1 /* HOISTED */))])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "onClick", "_cache", "$event", "$options", "navigateToPage", "_hoisted_3", "_createVNode", "_component_ChatBubbleLeftRightIcon", "_hoisted_4", "_component_PresentationChartBarIcon", "_hoisted_5", "_component_DocumentCheckIcon", "_createTextVNode", "_hoisted_6", "_component_AcademicCapIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\HomeView.vue"], "sourcesContent": ["<template>\n  <div class=\"home\">\n    <div class=\"header\">\n      <h1>师说教学辅助系统</h1>\n      <p class=\"subtitle\">智能教学助手，助力教育创新</p>\n    </div>\n\n    <div class=\"button-container\">\n      <div class=\"feature-card\" @click=\"navigateToPage('/aichat')\">\n        <div class=\"icon-container\">\n          <ChatBubbleLeftRightIcon class=\"icon\" />\n        </div>\n        <div class=\"content\">\n          <h3>知识问答</h3>\n          <p>智能解答教学相关问题，提供专业指导</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card\" @click=\"navigateToPage('/pptcreate')\">\n        <div class=\"icon-container\">\n          <PresentationChartBarIcon class=\"icon\" />\n        </div>\n        <div class=\"content\">\n          <h3>PPT生成</h3>\n          <p>快速生成教学课件，提升备课效率</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card\" @click=\"navigateToPage('/aichat')\">\n        <div class=\"icon-container\">\n          <DocumentCheckIcon class=\"icon\" />jio\n        </div>\n        <div class=\"content\">\n          <h3>作业批改</h3>\n          <p>智能批改学生作业，提供详细反馈</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card\" @click=\"navigateToPage('/jiaoan')\">\n        <div class=\"icon-container\">\n          <AcademicCapIcon class=\"icon\" />\n        </div>\n        <div class=\"content\">\n          <h3>智能教案生成</h3>\n          <p>AI驱动的教案生成工具，快速创建完整教案</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  ChatBubbleLeftRightIcon,\n  PresentationChartBarIcon,\n  DocumentCheckIcon,\n  AcademicCapIcon\n} from '@heroicons/vue/24/outline';\n\nexport default {\n  name: 'HomePage',\n  components: {\n    ChatBubbleLeftRightIcon,\n    PresentationChartBarIcon,\n    DocumentCheckIcon,\n    AcademicCapIcon\n  },\n  methods: {\n    navigateToPage(path) {\n      this.$router.push(path);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.home {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 40px 20px;\n  background: linear-gradient(to bottom, #f8f9fa, #ffffff);\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 50px;\n  padding: 20px;\n}\n\nh1 {\n  font-size: 2.8rem;\n  color: #2c3e50;\n  margin-bottom: 15px;\n  font-weight: 600;\n}\n\n.subtitle {\n  font-size: 1.3rem;\n  color: #666;\n  font-weight: 400;\n}\n\n.button-container {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 20px;\n  padding: 20px;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n\n.feature-card {\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  border: 1px solid #eaeaea;\n  height: 100%;\n}\n\n.feature-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);\n  border-color: #42b983;\n}\n\n.icon-container {\n  width: 70px;\n  height: 70px;\n  background: #f0f9ff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 25px;\n  transition: all 0.3s ease;\n}\n\n.feature-card:hover .icon-container {\n  background: #e6f7f0;\n  transform: scale(1.1);\n}\n\n.icon {\n  width: 35px;\n  height: 35px;\n  color: #42b983;\n}\n\n.content h3 {\n  font-size: 1.4rem;\n  color: #2c3e50;\n  margin-bottom: 12px;\n  font-weight: 600;\n}\n\n.content p {\n  color: #666;\n  font-size: 1rem;\n  line-height: 1.6;\n}\n\n@media (max-width: 1200px) {\n  .button-container {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 768px) {\n  .button-container {\n    grid-template-columns: 1fr;\n  }\n\n  h1 {\n    font-size: 2.2rem;\n  }\n\n  .subtitle {\n    font-size: 1.1rem;\n  }\n\n  .feature-card {\n    padding: 20px;\n  }\n}\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAM;;EAMVA,KAAK,EAAC;AAAkB;;EAEpBA,KAAK,EAAC;AAAgB;;EAUtBA,KAAK,EAAC;AAAgB;;EAUtBA,KAAK,EAAC;AAAgB;;EAUtBA,KAAK,EAAC;AAAgB;;;;;;uBAtCjCC,mBAAA,CA+CM,OA/CNC,UA+CM,G,0BA9CJC,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAQ,IACjBG,mBAAA,CAAiB,YAAb,UAAQ,GACZA,mBAAA,CAAqC;IAAlCH,KAAK,EAAC;EAAU,GAAC,eAAa,E,sBAGnCG,mBAAA,CAwCM,OAxCNC,UAwCM,GAvCJD,mBAAA,CAQM;IARDH,KAAK,EAAC,cAAc;IAAEK,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,cAAc;MAC9CN,mBAAA,CAEM,OAFNO,UAEM,GADJC,YAAA,CAAwCC,kCAAA;IAAfZ,KAAK,EAAC;EAAM,G,6BAEvCG,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAS,IAClBG,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAwB,WAArB,mBAAiB,E,wBAIxBA,mBAAA,CAQM;IARDH,KAAK,EAAC,cAAc;IAAEK,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,cAAc;MAC9CN,mBAAA,CAEM,OAFNU,UAEM,GADJF,YAAA,CAAyCG,mCAAA;IAAfd,KAAK,EAAC;EAAM,G,6BAExCG,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAS,IAClBG,mBAAA,CAAc,YAAV,OAAK,GACTA,mBAAA,CAAsB,WAAnB,iBAAe,E,wBAItBA,mBAAA,CAQM;IARDH,KAAK,EAAC,cAAc;IAAEK,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,cAAc;MAC9CN,mBAAA,CAEM,OAFNY,UAEM,GADJJ,YAAA,CAAkCK,4BAAA;IAAfhB,KAAK,EAAC;EAAM,I,0BA9BzCiB,gBAAA,CA8B4C,MACpC,G,6BACAd,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAS,IAClBG,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAsB,WAAnB,iBAAe,E,wBAItBA,mBAAA,CAQM;IARDH,KAAK,EAAC,cAAc;IAAEK,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,cAAc;MAC9CN,mBAAA,CAEM,OAFNe,UAEM,GADJP,YAAA,CAAgCQ,0BAAA;IAAfnB,KAAK,EAAC;EAAM,G,6BAE/BG,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAS,IAClBG,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAA2B,WAAxB,sBAAoB,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}