{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport HomeView from '../views/HomeView.vue';\nimport Chat from \"@/views/AiChat.vue\";\nimport PPTGnerate from \"@/views/PPTGnerate.vue\";\nimport PPTCreate from '@/views/PPTCreate.vue';\nimport LoginVue from '@/views/Login.vue';\nimport LayoutVue from '@/views/Layout.vue';\nimport ADPage from '@/components/home/<USER>';\nimport ArticleCategoryVue from '@/views/article/ArticleCategory.vue';\nimport ArticleManageVue from '@/views/article/ArticleManage.vue';\nimport UserAvatarVue from '@/views/user/UserAvatar.vue';\nimport UserInfoVue from '@/views/user/UserInfo.vue';\nimport UserResetPasswordVue from '@/views/user/UserResetPassword.vue';\nimport LessonPlanGenerator from '@/views/LessonPlanGenerator.vue';\nconst routes = [{\n  path: '/',\n  name: 'homepage',\n  component: HomeView\n}, {\n  path: '/login',\n  component: LoginVue\n}, {\n  path: '/admin',\n  component: LayoutVue,\n  redirect: '/admin/article/manage',\n  children: [{\n    path: '/admin/article/category',\n    component: ArticleCategoryVue\n  }, {\n    path: '/admin/article/manage',\n    component: ArticleManageVue\n  }, {\n    path: '/admin/lesson/generator',\n    component: LessonPlanGenerator\n  }, {\n    path: '/admin/user/info',\n    component: UserInfoVue\n  }, {\n    path: '/admin/user/avatar',\n    component: UserAvatarVue\n  }, {\n    path: '/admin/user/resetPassword',\n    component: UserResetPasswordVue\n  }]\n}, {\n  path: '/aichat',\n  name: 'aichat',\n  component: Chat\n}, {\n  path: '/pptgenerate',\n  name: 'pptgenerate',\n  component: PPTGnerate\n}, {\n  path: '/pptcreate',\n  name: 'pptcreate',\n  component: PPTCreate\n}, {\n  path: '/jiaoan',\n  name: 'jiaoan',\n  component: LessonPlanGenerator\n}, {\n  path: '/Home',\n  component: ADPage\n}];\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "HomeView", "Cha<PERSON>", "PPTGnerate", "PPTCreate", "LoginVue", "LayoutVue", "ADPage", "ArticleCategoryVue", "ArticleManageVue", "UserAvatarVue", "UserInfoVue", "UserResetPasswordVue", "LessonPlanGenerator", "routes", "path", "name", "component", "redirect", "children", "router", "history", "process", "env", "BASE_URL"], "sources": ["C:/Users/<USER>/Desktop/shishuo/vue/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\nimport HomeView from '../views/HomeView.vue'\nimport Chat from \"@/views/AiChat.vue\";\nimport PPTGnerate from \"@/views/PPTGnerate.vue\";\nimport PPTCreate from '@/views/PPTCreate.vue';\nimport LoginVue from '@/views/Login.vue'\nimport LayoutVue from '@/views/Layout.vue'\nimport ADPage from '@/components/home/<USER>';\nimport ArticleCategoryVue from '@/views/article/ArticleCategory.vue'\nimport ArticleManageVue from '@/views/article/ArticleManage.vue'\nimport UserAvatarVue from '@/views/user/UserAvatar.vue'\nimport UserInfoVue from '@/views/user/UserInfo.vue'\nimport UserResetPasswordVue from '@/views/user/UserResetPassword.vue'\nimport LessonPlanGenerator from '@/views/LessonPlanGenerator.vue'\nconst routes = [\n  {\n    path: '/',\n    name: 'homepage',\n    component: HomeView\n  },\n\n  { path: '/login', component: LoginVue },\n        {\n        path: '/admin', component: LayoutVue,redirect:'/admin/article/manage', children: [\n            { path: '/admin/article/category', component: ArticleCategoryVue },\n            { path: '/admin/article/manage', component: ArticleManageVue },\n            { path: '/admin/lesson/generator', component: LessonPlanGenerator },\n            { path: '/admin/user/info', component: UserInfoVue },\n            { path: '/admin/user/avatar', component: UserAvatarVue },\n            { path: '/admin/user/resetPassword', component: UserResetPasswordVue }\n        ]\n    },\n   {\n        path: '/aichat',\n        name: 'aichat',\n        component: Chat\n    },\n    {\n        path: '/pptgenerate',\n        name: 'pptgenerate',\n        component: PPTGnerate\n    }\n\n  ,\n\n\n\n    {\n        path: '/pptcreate',\n        name: 'pptcreate',\n        component: PPTCreate\n    },\n    {\n        path: '/jiaoan',\n        name: 'jiaoan',\n        component: LessonPlanGenerator\n    },\n    {   path: '/Home', component: ADPage}\n\n]\n\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n})\n\nexport default router\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,oBAAoB,MAAM,oCAAoC;AACrE,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEhB;AACb,CAAC,EAED;EAAEc,IAAI,EAAE,QAAQ;EAAEE,SAAS,EAAEZ;AAAS,CAAC,EACjC;EACAU,IAAI,EAAE,QAAQ;EAAEE,SAAS,EAAEX,SAAS;EAACY,QAAQ,EAAC,uBAAuB;EAAEC,QAAQ,EAAE,CAC7E;IAAEJ,IAAI,EAAE,yBAAyB;IAAEE,SAAS,EAAET;EAAmB,CAAC,EAClE;IAAEO,IAAI,EAAE,uBAAuB;IAAEE,SAAS,EAAER;EAAiB,CAAC,EAC9D;IAAEM,IAAI,EAAE,yBAAyB;IAAEE,SAAS,EAAEJ;EAAoB,CAAC,EACnE;IAAEE,IAAI,EAAE,kBAAkB;IAAEE,SAAS,EAAEN;EAAY,CAAC,EACpD;IAAEI,IAAI,EAAE,oBAAoB;IAAEE,SAAS,EAAEP;EAAc,CAAC,EACxD;IAAEK,IAAI,EAAE,2BAA2B;IAAEE,SAAS,EAAEL;EAAqB,CAAC;AAE9E,CAAC,EACF;EACKG,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEf;AACf,CAAC,EACD;EACIa,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEd;AACf,CAAC,EAMD;EACIY,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEb;AACf,CAAC,EACD;EACIW,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEJ;AACf,CAAC,EACD;EAAIE,IAAI,EAAE,OAAO;EAAEE,SAAS,EAAEV;AAAM,CAAC,CAExC;AAED,MAAMa,MAAM,GAAGrB,YAAY,CAAC;EAC1BsB,OAAO,EAAErB,gBAAgB,CAACsB,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/CV;AACF,CAAC,CAAC;AAEF,eAAeM,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}