{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"el-dropdown__box\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Management = _resolveComponent(\"Management\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_menu_item = _resolveComponent(\"el-menu-item\");\n  const _component_Promotion = _resolveComponent(\"Promotion\");\n  const _component_Document = _resolveComponent(\"Document\");\n  const _component_UserFilled = _resolveComponent(\"UserFilled\");\n  const _component_User = _resolveComponent(\"User\");\n  const _component_Crop = _resolveComponent(\"Crop\");\n  const _component_EditPen = _resolveComponent(\"EditPen\");\n  const _component_el_sub_menu = _resolveComponent(\"el-sub-menu\");\n  const _component_el_menu = _resolveComponent(\"el-menu\");\n  const _component_el_aside = _resolveComponent(\"el-aside\");\n  const _component_el_avatar = _resolveComponent(\"el-avatar\");\n  const _component_CaretBottom = _resolveComponent(\"CaretBottom\");\n  const _component_el_dropdown_item = _resolveComponent(\"el-dropdown-item\");\n  const _component_el_dropdown_menu = _resolveComponent(\"el-dropdown-menu\");\n  const _component_el_dropdown = _resolveComponent(\"el-dropdown\");\n  const _component_el_header = _resolveComponent(\"el-header\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  const _component_el_main = _resolveComponent(\"el-main\");\n  const _component_el_footer = _resolveComponent(\"el-footer\");\n  const _component_el_container = _resolveComponent(\"el-container\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createCommentVNode(\" element-plus中的容器 \"), _createVNode(_component_el_container, {\n    class: \"layout-container\"\n  }, {\n    default: _withCtx(() => [_createCommentVNode(\" 左侧菜单 \"), _createVNode(_component_el_aside, {\n      width: \"200px\"\n    }, {\n      default: _withCtx(() => [_cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n        class: \"el-aside__logo\"\n      }, null, -1 /* HOISTED */)), _createCommentVNode(\" element-plus的菜单标签 \"), _createVNode(_component_el_menu, {\n        \"active-text-color\": \"#ffd04b\",\n        \"background-color\": \"#232323\",\n        \"text-color\": \"#fff\",\n        router: \"\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n          index: \"/article/category\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_Management)]),\n            _: 1 /* STABLE */\n          }), _cache[0] || (_cache[0] = _createElementVNode(\"span\", null, \"学习助手\", -1 /* HOISTED */))]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_menu_item, {\n          index: \"/article/manage\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_Promotion)]),\n            _: 1 /* STABLE */\n          }), _cache[1] || (_cache[1] = _createElementVNode(\"span\", null, \"教案管理\", -1 /* HOISTED */))]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_menu_item, {\n          index: \"/lesson/generator\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_Document)]),\n            _: 1 /* STABLE */\n          }), _cache[2] || (_cache[2] = _createElementVNode(\"span\", null, \"教案生成器\", -1 /* HOISTED */))]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_sub_menu, null, {\n          title: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_UserFilled)]),\n            _: 1 /* STABLE */\n          }), _cache[3] || (_cache[3] = _createElementVNode(\"span\", null, \"个人中心\", -1 /* HOISTED */))]),\n          default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n            index: \"/user/info\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode(_component_User)]),\n              _: 1 /* STABLE */\n            }), _cache[4] || (_cache[4] = _createElementVNode(\"span\", null, \"基本资料\", -1 /* HOISTED */))]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_menu_item, {\n            index: \"/user/avatar\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode(_component_Crop)]),\n              _: 1 /* STABLE */\n            }), _cache[5] || (_cache[5] = _createElementVNode(\"span\", null, \"更换头像\", -1 /* HOISTED */))]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_menu_item, {\n            index: \"/user/resetPassword\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode(_component_EditPen)]),\n              _: 1 /* STABLE */\n            }), _cache[6] || (_cache[6] = _createElementVNode(\"span\", null, \"重置密码\", -1 /* HOISTED */))]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 右侧主区域 \"), _createVNode(_component_el_container, null, {\n      default: _withCtx(() => [_createCommentVNode(\" 头部区域 \"), _createVNode(_component_el_header, null, {\n        default: _withCtx(() => [_createElementVNode(\"div\", null, [_cache[8] || (_cache[8] = _createTextVNode(\"身份：\")), _createElementVNode(\"strong\", null, _toDisplayString($setup.userInfoStore.info.nickname), 1 /* TEXT */)]), _createCommentVNode(\" 下拉菜单 \"), _createCommentVNode(\" command: 条目被点击后会触发,在事件函数上可以声明一个参数,接收条目对应的指令 \"), _createVNode(_component_el_dropdown, {\n          placement: \"bottom-end\",\n          onCommand: $setup.handleCommand\n        }, {\n          dropdown: _withCtx(() => [_createVNode(_component_el_dropdown_menu, null, {\n            default: _withCtx(() => [_createVNode(_component_el_dropdown_item, {\n              command: \"info\",\n              icon: $setup.User\n            }, {\n              default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\"基本资料\")])),\n              _: 1 /* STABLE */\n            }, 8 /* PROPS */, [\"icon\"]), _createVNode(_component_el_dropdown_item, {\n              command: \"avatar\",\n              icon: $setup.Crop\n            }, {\n              default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"更换头像\")])),\n              _: 1 /* STABLE */\n            }, 8 /* PROPS */, [\"icon\"]), _createVNode(_component_el_dropdown_item, {\n              command: \"resetPassword\",\n              icon: $setup.EditPen\n            }, {\n              default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"重置密码\")])),\n              _: 1 /* STABLE */\n            }, 8 /* PROPS */, [\"icon\"]), _createVNode(_component_el_dropdown_item, {\n              command: \"logout\",\n              icon: $setup.SwitchButton\n            }, {\n              default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"退出登录\")])),\n              _: 1 /* STABLE */\n            }, 8 /* PROPS */, [\"icon\"])]),\n            _: 1 /* STABLE */\n          })]),\n          default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_1, [_createVNode(_component_el_avatar, {\n            src: $setup.userInfoStore.info.userPic ? $setup.userInfoStore.info.userPic : $setup.avatar\n          }, null, 8 /* PROPS */, [\"src\"]), _createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_CaretBottom)]),\n            _: 1 /* STABLE */\n          })])]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"onCommand\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 中间区域 \"), _createVNode(_component_el_main, null, {\n        default: _withCtx(() => [_createCommentVNode(\" <div style=\\\"width: 1290px; height: 570px;border: 1px solid red;\\\">\\r\\n                    内容展示区\\r\\n                </div> \"), _createVNode(_component_router_view)]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 底部区域 \"), _createVNode(_component_el_footer, null, {\n        default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\"师说 ©2025 Created by 师说开发团队\")])),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_Fragment", "_createCommentVNode", "_createVNode", "_component_el_container", "default", "_withCtx", "_component_el_aside", "width", "_createElementVNode", "_component_el_menu", "router", "_component_el_menu_item", "index", "_component_el_icon", "_component_Management", "_", "_component_Promotion", "_component_Document", "_component_el_sub_menu", "title", "_component_UserFilled", "_component_User", "_component_Crop", "_component_EditPen", "_component_el_header", "_createTextVNode", "_toDisplayString", "$setup", "userInfoStore", "info", "nickname", "_component_el_dropdown", "placement", "onCommand", "handleCommand", "dropdown", "_component_el_dropdown_menu", "_component_el_dropdown_item", "command", "icon", "User", "_cache", "Crop", "EditPen", "SwitchButton", "_hoisted_1", "_component_el_avatar", "src", "userPic", "avatar", "_component_CaretBottom", "_component_el_main", "_component_router_view", "_component_el_footer"], "sources": ["C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\Layout.vue"], "sourcesContent": ["<script>\r\nimport {\r\n    Management,\r\n    Promotion,\r\n    UserFilled,\r\n    User,\r\n    Crop,\r\n    EditPen,\r\n    SwitchButton,\r\n    CaretBottom,\r\n    Document\r\n} from '@element-plus/icons-vue'\r\nimport avatar from '@/assets/default.png'\r\nimport {userInfoService} from '@/api/user.js'\r\nimport useUserInfoStore from '@/stores/userInfo.js'\r\nimport {useTokenStore} from '@/stores/token.js'\r\nimport {useRouter} from 'vue-router'\r\nimport {ElMessage,ElMessageBox} from 'element-plus'\r\n\r\nexport default {\r\n  name: 'LayoutView',\r\n  setup() {\r\n    const tokenStore = useTokenStore();\r\n    const userInfoStore = useUserInfoStore();\r\n    const router = useRouter();\r\n\r\n    //调用函数,获取用户详细信息\r\n    const getUserInfo = async() => {\r\n      //调用接口\r\n      let result = await userInfoService();\r\n      //数据存储到pinia中\r\n      userInfoStore.setInfo(result.data);\r\n    }\r\n\r\n    getUserInfo();\r\n\r\n    //条目被点击后,调用的函数\r\n    const handleCommand = (command) => {\r\n      //判断指令\r\n      if(command === 'logout'){\r\n        //退出登录\r\n        ElMessageBox.confirm(\r\n          '您确认要退出吗?',\r\n          '温馨提示',\r\n          {\r\n            confirmButtonText: '确认',\r\n            cancelButtonText: '取消',\r\n            type: 'warning',\r\n          }\r\n        )\r\n        .then(async () => {\r\n          //退出登录\r\n          //1.清空pinia中存储的token以及个人信息\r\n          tokenStore.removeToken()\r\n          userInfoStore.removeInfo()\r\n\r\n          //2.跳转到登录页面\r\n          router.push('/login')\r\n          ElMessage({\r\n            type: 'success',\r\n            message: '退出登录成功',\r\n          })\r\n\r\n        })\r\n        .catch(() => {\r\n          ElMessage({\r\n            type: 'info',\r\n            message: '用户取消了退出登录',\r\n          })\r\n        })\r\n      } else {\r\n        //路由\r\n        router.push('/user/'+command)\r\n      }\r\n    }\r\n\r\n    return {\r\n      userInfoStore,\r\n      avatar,\r\n      handleCommand,\r\n      // 图标组件\r\n      Management,\r\n      Promotion,\r\n      UserFilled,\r\n      User,\r\n      Crop,\r\n      EditPen,\r\n      SwitchButton,\r\n      CaretBottom,\r\n      Document\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n    <!-- element-plus中的容器 -->\r\n    <el-container class=\"layout-container\">\r\n        <!-- 左侧菜单 -->\r\n        <el-aside width=\"200px\">\r\n            <div class=\"el-aside__logo\"></div>\r\n            <!-- element-plus的菜单标签 -->\r\n            <el-menu active-text-color=\"#ffd04b\" background-color=\"#232323\"  text-color=\"#fff\"\r\n                router>\r\n                <el-menu-item index=\"/article/category\">\r\n                    <el-icon>\r\n                        <Management />\r\n                    </el-icon>\r\n                    <span>学习助手</span>\r\n                </el-menu-item>\r\n                <el-menu-item index=\"/article/manage\">\r\n                    <el-icon>\r\n                        <Promotion />\r\n                    </el-icon>\r\n                    <span>教案管理</span>\r\n                </el-menu-item>\r\n                <el-menu-item index=\"/lesson/generator\">\r\n                    <el-icon>\r\n                        <Document />\r\n                    </el-icon>\r\n                    <span>教案生成器</span>\r\n                </el-menu-item>\r\n                <el-sub-menu >\r\n                    <template #title>\r\n                        <el-icon>\r\n                            <UserFilled />\r\n                        </el-icon>\r\n                        <span>个人中心</span>\r\n                    </template>\r\n                    <el-menu-item index=\"/user/info\">\r\n                        <el-icon>\r\n                            <User />\r\n                        </el-icon>\r\n                        <span>基本资料</span>\r\n                    </el-menu-item>\r\n                    <el-menu-item index=\"/user/avatar\">\r\n                        <el-icon>\r\n                            <Crop />\r\n                        </el-icon>\r\n                        <span>更换头像</span>\r\n                    </el-menu-item>\r\n                    <el-menu-item index=\"/user/resetPassword\">\r\n                        <el-icon>\r\n                            <EditPen />\r\n                        </el-icon>\r\n                        <span>重置密码</span>\r\n                    </el-menu-item>\r\n                </el-sub-menu>\r\n            </el-menu>\r\n        </el-aside>\r\n        <!-- 右侧主区域 -->\r\n        <el-container>\r\n            <!-- 头部区域 -->\r\n            <el-header>\r\n                <div>身份：<strong>{{ userInfoStore.info.nickname }}</strong></div>\r\n                <!-- 下拉菜单 -->\r\n                <!-- command: 条目被点击后会触发,在事件函数上可以声明一个参数,接收条目对应的指令 -->\r\n                <el-dropdown placement=\"bottom-end\" @command=\"handleCommand\">\r\n                    <span class=\"el-dropdown__box\">\r\n                        <el-avatar :src=\"userInfoStore.info.userPic? userInfoStore.info.userPic:avatar\" />\r\n                        <el-icon>\r\n                            <CaretBottom />\r\n                        </el-icon>\r\n                    </span>\r\n                    <template #dropdown>\r\n                        <el-dropdown-menu>\r\n                            <el-dropdown-item command=\"info\" :icon=\"User\">基本资料</el-dropdown-item>\r\n                            <el-dropdown-item command=\"avatar\" :icon=\"Crop\">更换头像</el-dropdown-item>\r\n                            <el-dropdown-item command=\"resetPassword\" :icon=\"EditPen\">重置密码</el-dropdown-item>\r\n                            <el-dropdown-item command=\"logout\" :icon=\"SwitchButton\">退出登录</el-dropdown-item>\r\n                        </el-dropdown-menu>\r\n                    </template>\r\n                </el-dropdown>\r\n            </el-header>\r\n            <!-- 中间区域 -->\r\n            <el-main>\r\n                <!-- <div style=\"width: 1290px; height: 570px;border: 1px solid red;\">\r\n                    内容展示区\r\n                </div> -->\r\n                <router-view></router-view>\r\n            </el-main>\r\n            <!-- 底部区域 -->\r\n            <el-footer>师说 ©2025 Created by 师说开发团队</el-footer>\r\n        </el-container>\r\n    </el-container>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout-container {\r\n    height: 100vh;\r\n\r\n    .el-aside {\r\n        background-color: #232323;\r\n\r\n        &__logo {\r\n            height: 120px;\r\n            background: url('@/assets/sslogo.png') no-repeat center / 120px auto;\r\n        }\r\n\r\n        .el-menu {\r\n            border-right: none;\r\n        }\r\n    }\r\n\r\n    .el-header {\r\n        background-color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n\r\n        .el-dropdown__box {\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            .el-icon {\r\n                color: #999;\r\n                margin-left: 10px;\r\n            }\r\n\r\n            &:active,\r\n            &:focus {\r\n                outline: none;\r\n            }\r\n        }\r\n    }\r\n\r\n    .el-footer {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: 14px;\r\n        color: #666;\r\n    }\r\n}\r\n</style>"], "mappings": ";;EA8J0BA,KAAK,EAAC;AAAkB;;;;;;;;;;;;;;;;;;;;;;;;uBA9JlDC,mBAAA,CAAAC,SAAA,SAgGIC,mBAAA,sBAAyB,EACzBC,YAAA,CAuFeC,uBAAA;IAvFDL,KAAK,EAAC;EAAkB;IAjG1CM,OAAA,EAAAC,QAAA,CAkGQ,MAAa,CAAbJ,mBAAA,UAAa,EACbC,YAAA,CAkDWI,mBAAA;MAlDDC,KAAK,EAAC;IAAO;MAnG/BH,OAAA,EAAAC,QAAA,CAoGY,MAAkC,C,0BAAlCG,mBAAA,CAAkC;QAA7BV,KAAK,EAAC;MAAgB,6BAC3BG,mBAAA,uBAA0B,EAC1BC,YAAA,CA8CUO,kBAAA;QA9CD,mBAAiB,EAAC,SAAS;QAAC,kBAAgB,EAAC,SAAS;QAAE,YAAU,EAAC,MAAM;QAC9EC,MAAM,EAAN;;QAvGhBN,OAAA,EAAAC,QAAA,CAwGgB,MAKe,CALfH,YAAA,CAKeS,uBAAA;UALDC,KAAK,EAAC;QAAmB;UAxGvDR,OAAA,EAAAC,QAAA,CAyGoB,MAEU,CAFVH,YAAA,CAEUW,kBAAA;YA3G9BT,OAAA,EAAAC,QAAA,CA0GwB,MAAc,CAAdH,YAAA,CAAcY,qBAAA,E;YA1GtCC,CAAA;wCA4GoBP,mBAAA,CAAiB,cAAX,MAAI,qB;UA5G9BO,CAAA;YA8GgBb,YAAA,CAKeS,uBAAA;UALDC,KAAK,EAAC;QAAiB;UA9GrDR,OAAA,EAAAC,QAAA,CA+GoB,MAEU,CAFVH,YAAA,CAEUW,kBAAA;YAjH9BT,OAAA,EAAAC,QAAA,CAgHwB,MAAa,CAAbH,YAAA,CAAac,oBAAA,E;YAhHrCD,CAAA;wCAkHoBP,mBAAA,CAAiB,cAAX,MAAI,qB;UAlH9BO,CAAA;YAoHgBb,YAAA,CAKeS,uBAAA;UALDC,KAAK,EAAC;QAAmB;UApHvDR,OAAA,EAAAC,QAAA,CAqHoB,MAEU,CAFVH,YAAA,CAEUW,kBAAA;YAvH9BT,OAAA,EAAAC,QAAA,CAsHwB,MAAY,CAAZH,YAAA,CAAYe,mBAAA,E;YAtHpCF,CAAA;wCAwHoBP,mBAAA,CAAkB,cAAZ,OAAK,qB;UAxH/BO,CAAA;YA0HgBb,YAAA,CAyBcgB,sBAAA;UAxBCC,KAAK,EAAAd,QAAA,CACZ,MAEU,CAFVH,YAAA,CAEUW,kBAAA;YA9HlCT,OAAA,EAAAC,QAAA,CA6H4B,MAAc,CAAdH,YAAA,CAAckB,qBAAA,E;YA7H1CL,CAAA;wCA+HwBP,mBAAA,CAAiB,cAAX,MAAI,qB;UA/HlCJ,OAAA,EAAAC,QAAA,CAiIoB,MAKe,CALfH,YAAA,CAKeS,uBAAA;YALDC,KAAK,EAAC;UAAY;YAjIpDR,OAAA,EAAAC,QAAA,CAkIwB,MAEU,CAFVH,YAAA,CAEUW,kBAAA;cApIlCT,OAAA,EAAAC,QAAA,CAmI4B,MAAQ,CAARH,YAAA,CAAQmB,eAAA,E;cAnIpCN,CAAA;0CAqIwBP,mBAAA,CAAiB,cAAX,MAAI,qB;YArIlCO,CAAA;cAuIoBb,YAAA,CAKeS,uBAAA;YALDC,KAAK,EAAC;UAAc;YAvItDR,OAAA,EAAAC,QAAA,CAwIwB,MAEU,CAFVH,YAAA,CAEUW,kBAAA;cA1IlCT,OAAA,EAAAC,QAAA,CAyI4B,MAAQ,CAARH,YAAA,CAAQoB,eAAA,E;cAzIpCP,CAAA;0CA2IwBP,mBAAA,CAAiB,cAAX,MAAI,qB;YA3IlCO,CAAA;cA6IoBb,YAAA,CAKeS,uBAAA;YALDC,KAAK,EAAC;UAAqB;YA7I7DR,OAAA,EAAAC,QAAA,CA8IwB,MAEU,CAFVH,YAAA,CAEUW,kBAAA;cAhJlCT,OAAA,EAAAC,QAAA,CA+I4B,MAAW,CAAXH,YAAA,CAAWqB,kBAAA,E;cA/IvCR,CAAA;0CAiJwBP,mBAAA,CAAiB,cAAX,MAAI,qB;YAjJlCO,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QAsJQd,mBAAA,WAAc,EACdC,YAAA,CAgCeC,uBAAA;MAvLvBC,OAAA,EAAAC,QAAA,CAwJY,MAAa,CAAbJ,mBAAA,UAAa,EACbC,YAAA,CAoBYsB,oBAAA;QA7KxBpB,OAAA,EAAAC,QAAA,CA0JgB,MAAgE,CAAhEG,mBAAA,CAAgE,c,0BA1JhFiB,gBAAA,CA0JqB,KAAG,IAAAjB,mBAAA,CAAkD,gBAAAkB,gBAAA,CAAvCC,MAAA,CAAAC,aAAa,CAACC,IAAI,CAACC,QAAQ,iB,GAC9C7B,mBAAA,UAAa,EACbA,mBAAA,iDAAoD,EACpDC,YAAA,CAec6B,sBAAA;UAfDC,SAAS,EAAC,YAAY;UAAEC,SAAO,EAAEN,MAAA,CAAAO;;UAO/BC,QAAQ,EAAA9B,QAAA,CACf,MAKmB,CALnBH,YAAA,CAKmBkC,2BAAA;YA1K3ChC,OAAA,EAAAC,QAAA,CAsK4B,MAAqE,CAArEH,YAAA,CAAqEmC,2BAAA;cAAnDC,OAAO,EAAC,MAAM;cAAEC,IAAI,EAAEZ,MAAA,CAAAa;;cAtKpEpC,OAAA,EAAAC,QAAA,CAsK0E,MAAIoC,MAAA,QAAAA,MAAA,OAtK9EhB,gBAAA,CAsK0E,MAAI,E;cAtK9EV,CAAA;yCAuK4Bb,YAAA,CAAuEmC,2BAAA;cAArDC,OAAO,EAAC,QAAQ;cAAEC,IAAI,EAAEZ,MAAA,CAAAe;;cAvKtEtC,OAAA,EAAAC,QAAA,CAuK4E,MAAIoC,MAAA,SAAAA,MAAA,QAvKhFhB,gBAAA,CAuK4E,MAAI,E;cAvKhFV,CAAA;yCAwK4Bb,YAAA,CAAiFmC,2BAAA;cAA/DC,OAAO,EAAC,eAAe;cAAEC,IAAI,EAAEZ,MAAA,CAAAgB;;cAxK7EvC,OAAA,EAAAC,QAAA,CAwKsF,MAAIoC,MAAA,SAAAA,MAAA,QAxK1FhB,gBAAA,CAwKsF,MAAI,E;cAxK1FV,CAAA;yCAyK4Bb,YAAA,CAA+EmC,2BAAA;cAA7DC,OAAO,EAAC,QAAQ;cAAEC,IAAI,EAAEZ,MAAA,CAAAiB;;cAzKtExC,OAAA,EAAAC,QAAA,CAyKoF,MAAIoC,MAAA,SAAAA,MAAA,QAzKxFhB,gBAAA,CAyKoF,MAAI,E;cAzKxFV,CAAA;;YAAAA,CAAA;;UAAAX,OAAA,EAAAC,QAAA,CA8JoB,MAKO,CALPG,mBAAA,CAKO,QALPqC,UAKO,GAJH3C,YAAA,CAAkF4C,oBAAA;YAAtEC,GAAG,EAAEpB,MAAA,CAAAC,aAAa,CAACC,IAAI,CAACmB,OAAO,GAAErB,MAAA,CAAAC,aAAa,CAACC,IAAI,CAACmB,OAAO,GAACrB,MAAA,CAAAsB;4CACxE/C,YAAA,CAEUW,kBAAA;YAlKlCT,OAAA,EAAAC,QAAA,CAiK4B,MAAe,CAAfH,YAAA,CAAegD,sBAAA,E;YAjK3CnC,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UA8KYd,mBAAA,UAAa,EACbC,YAAA,CAKUiD,kBAAA;QApLtB/C,OAAA,EAAAC,QAAA,CAgLgB,MAEU,CAFVJ,mBAAA,gIAEU,EACVC,YAAA,CAA2BkD,sBAAA,E;QAnL3CrC,CAAA;UAqLYd,mBAAA,UAAa,EACbC,YAAA,CAAiDmD,oBAAA;QAtL7DjD,OAAA,EAAAC,QAAA,CAsLuB,MAA0BoC,MAAA,SAAAA,MAAA,QAtLjDhB,gBAAA,CAsLuB,4BAA0B,E;QAtLjDV,CAAA;;MAAAA,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}