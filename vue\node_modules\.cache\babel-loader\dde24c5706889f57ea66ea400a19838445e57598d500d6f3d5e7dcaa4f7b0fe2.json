{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { useRouter } from 'vue-router';\nimport WelcomeItem from './WelcomeItem.vue';\nimport DocumentationIcon from '../icons/IconDocumentation.vue';\nimport ToolingIcon from '../icons/IconTooling.vue';\nimport EcosystemIcon from '../icons/IconEcosystem.vue';\nimport CommunityIcon from '../icons/IconCommunity.vue';\nimport SupportIcon from '../icons/IconSupport.vue';\nexport default {\n  __name: 'homeRight',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const router = useRouter();\n\n    // 跳转到教案生成器\n    const goToLessonGenerator = () => {\n      router.push('/lesson/generator');\n    };\n\n    // 跳转到AI聊天\n    const goToAIChat = () => {\n      router.push('/aichat');\n    };\n\n    // 跳转到PPT生成\n    const goToPPTGenerate = () => {\n      router.push('/pptgenerate');\n    };\n    const __returned__ = {\n      router,\n      goToLessonGenerator,\n      goToAIChat,\n      goToPPTGenerate,\n      get useRouter() {\n        return useRouter;\n      },\n      WelcomeItem,\n      DocumentationIcon,\n      ToolingIcon,\n      EcosystemIcon,\n      CommunityIcon,\n      SupportIcon\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["useRouter", "WelcomeItem", "DocumentationIcon", "ToolingIcon", "EcosystemIcon", "CommunityIcon", "SupportIcon", "router", "goToLessonGenerator", "push", "goToAIChat", "goToPPTGenerate"], "sources": ["C:/Users/<USER>/Desktop/shishuo/vue/src/components/home/<USER>"], "sourcesContent": ["<script setup>\nimport { useRouter } from 'vue-router'\nimport WelcomeItem from './WelcomeItem.vue'\nimport DocumentationIcon from '../icons/IconDocumentation.vue'\nimport ToolingIcon from '../icons/IconTooling.vue'\nimport EcosystemIcon from '../icons/IconEcosystem.vue'\nimport CommunityIcon from '../icons/IconCommunity.vue'\nimport SupportIcon from '../icons/IconSupport.vue'\n\nconst router = useRouter()\n\n// 跳转到教案生成器\nconst goToLessonGenerator = () => {\n  router.push('/lesson/generator')\n}\n\n// 跳转到AI聊天\nconst goToAIChat = () => {\n  router.push('/aichat')\n}\n\n// 跳转到PPT生成\nconst goToPPTGenerate = () => {\n  router.push('/pptgenerate')\n}\n</script>\n\n<template>\n  <WelcomeItem>\n    <template #icon>\n      <DocumentationIcon />\n    </template>\n    <template #heading>在线错题本</template>\n\n    在线错题本功能可以帮助学生高效记录和管理错误题目，支持分类整理、重点标记和复习提醒。通过数据分析，帮助学生找到薄弱知识点。\n  </WelcomeItem>\n\n  <WelcomeItem @click=\"goToLessonGenerator\" class=\"clickable-item\">\n    <template #icon>\n      <ToolingIcon />\n    </template>\n    <template #heading>智能教案生成器</template>\n\n    智能教案生成器为教师提供强大的备课工具，支持自动生成教学计划、教学目标设定、教学步骤规划和资源推荐。点击进入体验完整的教案生成功能。\n  </WelcomeItem>\n\n  <WelcomeItem @click=\"goToAIChat\" class=\"clickable-item\">\n    <template #icon>\n      <EcosystemIcon />\n    </template>\n    <template #heading>AI学习助手</template>\n\n    AI学习助手利用人工智能技术，为学生提供个性化的学习建议和实时答疑服务。无论是知识点解析还是学习路径规划，都能助你事半功倍。点击开始对话。\n  </WelcomeItem>\n\n  <WelcomeItem>\n    <template #icon>\n      <CommunityIcon />\n    </template>\n    <template #heading>讨论区</template>\n\n    讨论区是师生交流的重要平台，学生可以在这里提问、分享学习心得，教师也可以参与讨论，解答疑惑。让我们一起构建一个充满活力的学习社区！\n  </WelcomeItem>\n\n  <WelcomeItem>\n    <template #icon>\n      <SupportIcon />\n    </template>\n    <template #heading>语言风格的选择</template>\n\n    我们支持多种语言风格的教学内容，无论是严谨学术风还是轻松活泼风，都可以根据学生的需求进行选择和切换，打造最适合的学习体验。\n  </WelcomeItem>\n</template>"], "mappings": ";AACA,SAASA,SAAS,QAAQ,YAAW;AACrC,OAAOC,WAAW,MAAM,mBAAkB;AAC1C,OAAOC,iBAAiB,MAAM,gCAA+B;AAC7D,OAAOC,WAAW,MAAM,0BAAyB;AACjD,OAAOC,aAAa,MAAM,4BAA2B;AACrD,OAAOC,aAAa,MAAM,4BAA2B;AACrD,OAAOC,WAAW,MAAM,0BAAyB;;;;;;;IAEjD,MAAMC,MAAM,GAAGP,SAAS,CAAC;;IAEzB;IACA,MAAMQ,mBAAmB,GAAGA,CAAA,KAAM;MAChCD,MAAM,CAACE,IAAI,CAAC,mBAAmB;IACjC;;IAEA;IACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;MACvBH,MAAM,CAACE,IAAI,CAAC,SAAS;IACvB;;IAEA;IACA,MAAME,eAAe,GAAGA,CAAA,KAAM;MAC5BJ,MAAM,CAACE,IAAI,CAAC,cAAc;IAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}