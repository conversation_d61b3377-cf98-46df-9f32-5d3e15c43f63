{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ChatBubbleLeftRightIcon, PresentationChartBarIcon, DocumentCheckIcon, AcademicCapIcon } from '@heroicons/vue/24/outline';\nexport default {\n  name: 'HomePage',\n  components: {\n    ChatBubbleLeftRightIcon,\n    PresentationChartBarIcon,\n    DocumentCheckIcon,\n    AcademicCapIcon\n  },\n  methods: {\n    navigateToPage(path) {\n      this.$router.push(path);\n    }\n  }\n};", "map": {"version": 3, "names": ["ChatBubbleLeftRightIcon", "PresentationChartBarIcon", "DocumentCheckIcon", "AcademicCapIcon", "name", "components", "methods", "navigateToPage", "path", "$router", "push"], "sources": ["C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\HomeView.vue"], "sourcesContent": ["<template>\n  <div class=\"home\">\n    <div class=\"header\">\n      <h1>师说教学辅助系统</h1>\n      <p class=\"subtitle\">智能教学助手，助力教育创新</p>\n    </div>\n\n    <div class=\"button-container\">\n      <div class=\"feature-card\" @click=\"navigateToPage('/aichat')\">\n        <div class=\"icon-container\">\n          <ChatBubbleLeftRightIcon class=\"icon\" />\n        </div>\n        <div class=\"content\">\n          <h3>知识问答</h3>\n          <p>智能解答教学相关问题，提供专业指导</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card\" @click=\"navigateToPage('/pptcreate')\">\n        <div class=\"icon-container\">\n          <PresentationChartBarIcon class=\"icon\" />\n        </div>\n        <div class=\"content\">\n          <h3>PPT生成</h3>\n          <p>快速生成教学课件，提升备课效率</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card\" @click=\"navigateToPage('/aichat')\">\n        <div class=\"icon-container\">\n          <DocumentCheckIcon class=\"icon\" />jio\n        </div>\n        <div class=\"content\">\n          <h3>作业批改</h3>\n          <p>智能批改学生作业，提供详细反馈</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card\" @click=\"navigateToPage('/jiaoan')\">\n        <div class=\"icon-container\">\n          <AcademicCapIcon class=\"icon\" />\n        </div>\n        <div class=\"content\">\n          <h3>智能教案生成</h3>\n          <p>AI驱动的教案生成工具，快速创建完整教案</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  ChatBubbleLeftRightIcon,\n  PresentationChartBarIcon,\n  DocumentCheckIcon,\n  AcademicCapIcon\n} from '@heroicons/vue/24/outline';\n\nexport default {\n  name: 'HomePage',\n  components: {\n    ChatBubbleLeftRightIcon,\n    PresentationChartBarIcon,\n    DocumentCheckIcon,\n    AcademicCapIcon\n  },\n  methods: {\n    navigateToPage(path) {\n      this.$router.push(path);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.home {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 40px 20px;\n  background: linear-gradient(to bottom, #f8f9fa, #ffffff);\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 50px;\n  padding: 20px;\n}\n\nh1 {\n  font-size: 2.8rem;\n  color: #2c3e50;\n  margin-bottom: 15px;\n  font-weight: 600;\n}\n\n.subtitle {\n  font-size: 1.3rem;\n  color: #666;\n  font-weight: 400;\n}\n\n.button-container {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 20px;\n  padding: 20px;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n\n.feature-card {\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  border: 1px solid #eaeaea;\n  height: 100%;\n}\n\n.feature-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);\n  border-color: #42b983;\n}\n\n.icon-container {\n  width: 70px;\n  height: 70px;\n  background: #f0f9ff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 25px;\n  transition: all 0.3s ease;\n}\n\n.feature-card:hover .icon-container {\n  background: #e6f7f0;\n  transform: scale(1.1);\n}\n\n.icon {\n  width: 35px;\n  height: 35px;\n  color: #42b983;\n}\n\n.content h3 {\n  font-size: 1.4rem;\n  color: #2c3e50;\n  margin-bottom: 12px;\n  font-weight: 600;\n}\n\n.content p {\n  color: #666;\n  font-size: 1rem;\n  line-height: 1.6;\n}\n\n@media (max-width: 1200px) {\n  .button-container {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 768px) {\n  .button-container {\n    grid-template-columns: 1fr;\n  }\n\n  h1 {\n    font-size: 2.2rem;\n  }\n\n  .subtitle {\n    font-size: 1.1rem;\n  }\n\n  .feature-card {\n    padding: 20px;\n  }\n}\n</style>"], "mappings": ";AAoDA,SACEA,uBAAuB,EACvBC,wBAAwB,EACxBC,iBAAiB,EACjBC,eAAc,QACT,2BAA2B;AAElC,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE;IACVL,uBAAuB;IACvBC,wBAAwB;IACxBC,iBAAiB;IACjBC;EACF,CAAC;EACDG,OAAO,EAAE;IACPC,cAAcA,CAACC,IAAI,EAAE;MACnB,IAAI,CAACC,OAAO,CAACC,IAAI,CAACF,IAAI,CAAC;IACzB;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}