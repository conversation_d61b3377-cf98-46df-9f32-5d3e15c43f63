{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = [\"src\"];\nconst _hoisted_2 = [\"src\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  return _openBlock(), _createBlock(_component_el_card, {\n    class: \"page-container\"\n  }, {\n    header: _withCtx(() => _cache[1] || (_cache[1] = [_createElementVNode(\"div\", {\n      class: \"header\"\n    }, [_createElementVNode(\"span\", null, \"更换头像\")], -1 /* HOISTED */)])),\n    default: _withCtx(() => [_createVNode(_component_el_row, null, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 12\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_upload, {\n          ref: \"uploadRef\",\n          class: \"avatar-uploader\",\n          \"show-file-list\": false,\n          \"auto-upload\": true,\n          action: \"/api/upload\",\n          name: \"file\",\n          headers: {\n            'Authorization': $setup.tokenStore.token\n          },\n          \"on-success\": $setup.uploadSuccess\n        }, {\n          default: _withCtx(() => [$setup.imgUrl ? (_openBlock(), _createElementBlock(\"img\", {\n            key: 0,\n            src: $setup.imgUrl,\n            class: \"avatar\"\n          }, null, 8 /* PROPS */, _hoisted_1)) : (_openBlock(), _createElementBlock(\"img\", {\n            key: 1,\n            src: $setup.avatar,\n            width: \"278\"\n          }, null, 8 /* PROPS */, _hoisted_2))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"headers\"]), _cache[4] || (_cache[4] = _createElementVNode(\"br\", null, null, -1 /* HOISTED */)), _createVNode(_component_el_button, {\n          type: \"primary\",\n          icon: $setup.Plus,\n          size: \"large\",\n          onClick: _cache[0] || (_cache[0] = $event => $setup.uploadRef.$el.querySelector('input').click())\n        }, {\n          default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\" 选择图片 \")])),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"icon\"]), _createVNode(_component_el_button, {\n          type: \"success\",\n          icon: $setup.Upload,\n          size: \"large\",\n          onClick: $setup.updateAvatar\n        }, {\n          default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\" 上传头像 \")])),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"icon\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["_createBlock", "_component_el_card", "class", "header", "_withCtx", "_cache", "_createElementVNode", "default", "_createVNode", "_component_el_row", "_component_el_col", "span", "_component_el_upload", "ref", "action", "name", "headers", "$setup", "tokenStore", "token", "uploadSuccess", "imgUrl", "_createElementBlock", "key", "src", "_hoisted_1", "avatar", "width", "_hoisted_2", "_", "_component_el_button", "type", "icon", "Plus", "size", "onClick", "$event", "uploadRef", "$el", "querySelector", "click", "_createTextVNode", "Upload", "updateAvatar"], "sources": ["C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\views\\user\\UserAvatar.vue"], "sourcesContent": ["<script setup>\r\nimport { Plus, Upload } from '@element-plus/icons-vue'\r\nimport { ref } from 'vue'\r\nimport avatar from '@/assets/default.png?url' // ✅ 添加 ?url 后缀\r\n\r\nconst uploadRef = ref()\r\n\r\nimport { useTokenStore } from '@/stores/token.js'\r\nconst tokenStore = useTokenStore()\r\n\r\nimport useUserInfoStore from '@/stores/userInfo.js'\r\nconst userInfoStore = useUserInfoStore()\r\n\r\n// 用户头像地址\r\nconst imgUrl = ref(userInfoStore.info.userPic)\r\n\r\n// 图片上传成功的回调函数\r\nconst uploadSuccess = (result) => {\r\n  imgUrl.value = result.data\r\n}\r\n\r\nimport { userAvatarUpdateService } from '@/api/user.js'\r\nimport { ElMessage } from 'element-plus'\r\n\r\n// 头像修改\r\nconst updateAvatar = async () => {\r\n  // 调用接口\r\n  let result = await userAvatarUpdateService(imgUrl.value)\r\n  ElMessage.success(result.msg ? result.msg : '修改成功')\r\n\r\n  // 修改 pinia 中的数据\r\n  userInfoStore.info.userPic = imgUrl.value\r\n}\r\n</script>\r\n\r\n<template>\r\n    <el-card class=\"page-container\">\r\n        <template #header>\r\n            <div class=\"header\">\r\n                <span>更换头像</span>\r\n            </div>\r\n        </template>\r\n        <el-row>\r\n            <el-col :span=\"12\">\r\n                <el-upload \r\n                    ref=\"uploadRef\"\r\n                    class=\"avatar-uploader\" \r\n                    :show-file-list=\"false\"\r\n                    :auto-upload=\"true\"\r\n                    action=\"/api/upload\"\r\n                    name=\"file\"\r\n                    :headers=\"{'Authorization':tokenStore.token}\"\r\n                    :on-success=\"uploadSuccess\"\r\n                    >\r\n                    <img v-if=\"imgUrl\" :src=\"imgUrl\" class=\"avatar\" />\r\n                    <img v-else :src=\"avatar\" width=\"278\" />\r\n                </el-upload>\r\n                <br />\r\n                <el-button type=\"primary\" :icon=\"Plus\" size=\"large\"  @click=\"uploadRef.$el.querySelector('input').click()\">\r\n                    选择图片\r\n                </el-button>\r\n                <el-button type=\"success\" :icon=\"Upload\" size=\"large\" @click=\"updateAvatar\">\r\n                    上传头像\r\n                </el-button>\r\n            </el-col>\r\n        </el-row>\r\n    </el-card>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.avatar-uploader {\r\n    :deep() {\r\n        .avatar {\r\n            width: 278px;\r\n            height: 278px;\r\n            display: block;\r\n        }\r\n\r\n        .el-upload {\r\n            border: 1px dashed var(--el-border-color);\r\n            border-radius: 6px;\r\n            cursor: pointer;\r\n            position: relative;\r\n            overflow: hidden;\r\n            transition: var(--el-transition-duration-fast);\r\n        }\r\n\r\n        .el-upload:hover {\r\n            border-color: var(--el-color-primary);\r\n        }\r\n\r\n        .el-icon.avatar-uploader-icon {\r\n            font-size: 28px;\r\n            color: #8c939d;\r\n            width: 278px;\r\n            height: 278px;\r\n            text-align: center;\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": ";mBAAA;mBAAA;;;;;;;uBAoCIA,YAAA,CA8BUC,kBAAA;IA9BDC,KAAK,EAAC;EAAgB;IAChBC,MAAM,EAAAC,QAAA,CACb,MAEMC,MAAA,QAAAA,MAAA,OAFNC,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAQ,IACfI,mBAAA,CAAiB,cAAX,MAAI,E;IAvC1BC,OAAA,EAAAH,QAAA,CA0CQ,MAuBS,CAvBTI,YAAA,CAuBSC,iBAAA;MAjEjBF,OAAA,EAAAH,QAAA,CA2CY,MAqBS,CArBTI,YAAA,CAqBSE,iBAAA;QArBAC,IAAI,EAAE;MAAE;QA3C7BJ,OAAA,EAAAH,QAAA,CA4CgB,MAYY,CAZZI,YAAA,CAYYI,oBAAA;UAXRC,GAAG,EAAC,WAAW;UACfX,KAAK,EAAC,iBAAiB;UACtB,gBAAc,EAAE,KAAK;UACrB,aAAW,EAAE,IAAI;UAClBY,MAAM,EAAC,aAAa;UACpBC,IAAI,EAAC,MAAM;UACVC,OAAO;YAAA,iBAAmBC,MAAA,CAAAC,UAAU,CAACC;UAAK;UAC1C,YAAU,EAAEF,MAAA,CAAAG;;UApDjCb,OAAA,EAAAH,QAAA,CAuD+pB,MAAkD,CADlrBa,MAAA,CAAAI,MAAM,I,cAAjBC,mBAAA,CAAkD;YAtDtEC,GAAA;YAsDwCC,GAAG,EAAEP,MAAA,CAAAI,MAAM;YAAEnB,KAAK,EAAC;kCAtD3DuB,UAAA,M,cAuDoBH,mBAAA,CAAwC;YAvD5DC,GAAA;YAuDiCC,GAAG,EAAEP,MAAA,CAAAS,MAAM;YAAEC,KAAK,EAAC;kCAvDpDC,UAAA,G;UAAAC,CAAA;kEAyDgBvB,mBAAA,CAAM,sCACNE,YAAA,CAEYsB,oBAAA;UAFDC,IAAI,EAAC,SAAS;UAAEC,IAAI,EAAEf,MAAA,CAAAgB,IAAI;UAAEC,IAAI,EAAC,OAAO;UAAGC,OAAK,EAAA9B,MAAA,QAAAA,MAAA,MAAA+B,MAAA,IAAEnB,MAAA,CAAAoB,SAAS,CAACC,GAAG,CAACC,aAAa,UAAUC,KAAK;;UA1DvHjC,OAAA,EAAAH,QAAA,CA0D2H,MAE3GC,MAAA,QAAAA,MAAA,OA5DhBoC,gBAAA,CA0D2H,QAE3G,E;UA5DhBZ,CAAA;qCA6DgBrB,YAAA,CAEYsB,oBAAA;UAFDC,IAAI,EAAC,SAAS;UAAEC,IAAI,EAAEf,MAAA,CAAAyB,MAAM;UAAER,IAAI,EAAC,OAAO;UAAEC,OAAK,EAAElB,MAAA,CAAA0B;;UA7D9EpC,OAAA,EAAAH,QAAA,CA6D4F,MAE5EC,MAAA,QAAAA,MAAA,OA/DhBoC,gBAAA,CA6D4F,QAE5E,E;UA/DhBZ,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}