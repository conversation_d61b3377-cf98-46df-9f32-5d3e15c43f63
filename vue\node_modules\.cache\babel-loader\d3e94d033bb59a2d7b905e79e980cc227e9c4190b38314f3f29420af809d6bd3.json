{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveDynamicComponent as _resolveDynamicComponent, createBlock as _createBlock, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  class: \"feature-cards-container\"\n};\nconst _hoisted_2 = {\n  class: \"cards-grid\"\n};\nconst _hoisted_3 = [\"onClick\"];\nconst _hoisted_4 = {\n  class: \"card-icon\"\n};\nconst _hoisted_5 = {\n  class: \"card-content\"\n};\nconst _hoisted_6 = {\n  class: \"card-action\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"header\"\n  }, [_createElementVNode(\"h2\", null, \"师说教学辅助系统\"), _createElementVNode(\"p\", null, \"智能教学助手，助力教育创新\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_2, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList($setup.features, feature => {\n    return _createElementVNode(\"div\", {\n      key: feature.id,\n      class: \"feature-card\",\n      style: _normalizeStyle({\n        '--card-color': feature.color,\n        '--card-bg': feature.bgColor\n      }),\n      onClick: $event => $setup.navigateToFeature(feature.route)\n    }, [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_icon, {\n      size: 40,\n      color: feature.color\n    }, {\n      default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(feature.icon)))]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\"])]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"h3\", null, _toDisplayString(feature.title), 1 /* TEXT */), _createElementVNode(\"p\", null, _toDisplayString(feature.description), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      style: _normalizeStyle({\n        backgroundColor: feature.color,\n        borderColor: feature.color\n      })\n    }, {\n      default: _withCtx(() => [...(_cache[0] || (_cache[0] = [_createTextVNode(\" 立即体验 \")]))]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"style\"])])], 12 /* STYLE, PROPS */, _hoisted_3);\n  }), 64 /* STABLE_FRAGMENT */))])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_Fragment", "_renderList", "$setup", "features", "feature", "key", "id", "style", "_normalizeStyle", "color", "bgColor", "onClick", "$event", "navigateToFeature", "route", "_hoisted_4", "_createVNode", "_component_el_icon", "size", "default", "_withCtx", "_createBlock", "_resolveDynamicComponent", "icon", "_", "_hoisted_5", "_toDisplayString", "title", "description", "_hoisted_6", "_component_el_button", "type", "backgroundColor", "borderColor", "_cache", "_createTextVNode", "_hoisted_3"], "sources": ["C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\FeatureCards.vue"], "sourcesContent": ["<script setup>\nimport { useRouter } from 'vue-router'\nimport { Document, ChatDotRound, Monitor, BookOpen } from '@element-plus/icons-vue'\n\nconst router = useRouter()\n\n// 功能卡片数据\nconst features = [\n  {\n    id: 'lesson-generator',\n    title: '智能教案生成器',\n    description: '基于AI技术的智能教案生成工具，支持多学科、多年级的教案自动生成，包含教学目标、重点难点、教学步骤等完整内容。',\n    icon: Document,\n    route: '/jiaoan',\n    color: '#409eff',\n    bgColor: '#ecf5ff'\n  },\n  {\n    id: 'ai-chat',\n    title: 'AI学习助手',\n    description: 'AI驱动的智能问答系统，为师生提供实时的学习指导和答疑服务，支持多种学科知识点解析。',\n    icon: ChatDotRound,\n    route: '/aichat',\n    color: '#67c23a',\n    bgColor: '#f0f9eb'\n  },\n  {\n    id: 'ppt-generator',\n    title: 'PPT课件生成',\n    description: '快速生成精美的教学课件，支持多种模板和样式，让教学内容更加生动有趣。',\n    icon: Monitor,\n    route: '/pptgenerate',\n    color: '#e6a23c',\n    bgColor: '#fdf6ec'\n  },\n  {\n    id: 'knowledge-base',\n    title: '教学资源库',\n    description: '丰富的教学资源库，包含各学科的教学素材、习题库、参考资料等，助力教学质量提升。',\n    icon: BookOpen,\n    route: '/resources',\n    color: '#f56c6c',\n    bgColor: '#fef0f0'\n  }\n]\n\n// 跳转到指定功能\nconst navigateToFeature = (route) => {\n  router.push(route)\n}\n</script>\n\n<template>\n  <div class=\"feature-cards-container\">\n    <div class=\"header\">\n      <h2>师说教学辅助系统</h2>\n      <p>智能教学助手，助力教育创新</p>\n    </div>\n\n    <div class=\"cards-grid\">\n      <div\n        v-for=\"feature in features\"\n        :key=\"feature.id\"\n        class=\"feature-card\"\n        :style=\"{ '--card-color': feature.color, '--card-bg': feature.bgColor }\"\n        @click=\"navigateToFeature(feature.route)\"\n      >\n        <div class=\"card-icon\">\n          <el-icon :size=\"40\" :color=\"feature.color\">\n            <component :is=\"feature.icon\" />\n          </el-icon>\n        </div>\n\n        <div class=\"card-content\">\n          <h3>{{ feature.title }}</h3>\n          <p>{{ feature.description }}</p>\n        </div>\n\n        <div class=\"card-action\">\n          <el-button type=\"primary\" :style=\"{ backgroundColor: feature.color, borderColor: feature.color }\">\n            立即体验\n          </el-button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.feature-cards-container {\n  padding: 40px 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n\n  .header {\n    text-align: center;\n    margin-bottom: 50px;\n\n    h2 {\n      font-size: 32px;\n      color: #303133;\n      margin-bottom: 10px;\n      font-weight: 600;\n    }\n\n    p {\n      font-size: 16px;\n      color: #606266;\n      margin: 0;\n    }\n  }\n\n  .cards-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n    gap: 30px;\n\n    .feature-card {\n      background: white;\n      border-radius: 12px;\n      padding: 30px 25px;\n      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n      cursor: pointer;\n      transition: all 0.3s ease;\n      border: 2px solid transparent;\n      position: relative;\n      overflow: hidden;\n\n      &::before {\n        content: '';\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        height: 4px;\n        background: var(--card-color);\n      }\n\n      &:hover {\n        transform: translateY(-8px);\n        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n        border-color: var(--card-color);\n\n        .card-icon {\n          transform: scale(1.1);\n        }\n      }\n\n      .card-icon {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 80px;\n        height: 80px;\n        background: var(--card-bg);\n        border-radius: 50%;\n        margin: 0 auto 20px;\n        transition: transform 0.3s ease;\n      }\n\n      .card-content {\n        text-align: center;\n        margin-bottom: 25px;\n\n        h3 {\n          font-size: 20px;\n          color: #303133;\n          margin-bottom: 15px;\n          font-weight: 600;\n        }\n\n        p {\n          font-size: 14px;\n          color: #606266;\n          line-height: 1.6;\n          margin: 0;\n        }\n      }\n\n      .card-action {\n        text-align: center;\n\n        .el-button {\n          width: 100%;\n          font-weight: 500;\n        }\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .feature-cards-container {\n    padding: 20px 15px;\n\n    .header {\n      margin-bottom: 30px;\n\n      h2 {\n        font-size: 24px;\n      }\n    }\n\n    .cards-grid {\n      grid-template-columns: 1fr;\n      gap: 20px;\n\n      .feature-card {\n        padding: 25px 20px;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;EAqDOA,KAAK,EAAC;AAAyB;;EAM7BA,KAAK,EAAC;AAAY;mBA3D3B;;EAmEaA,KAAK,EAAC;AAAW;;EAMjBA,KAAK,EAAC;AAAc;;EAKpBA,KAAK,EAAC;AAAa;;;;uBAzB9BC,mBAAA,CAgCM,OAhCNC,UAgCM,G,0BA/BJC,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAQ,IACjBG,mBAAA,CAAiB,YAAb,UAAQ,GACZA,mBAAA,CAAoB,WAAjB,eAAa,E,sBAGlBA,mBAAA,CAyBM,OAzBNC,UAyBM,I,cAxBJH,mBAAA,CAuBMI,SAAA,QAnFZC,WAAA,CA6D0BC,MAAA,CAAAC,QAAQ,EAAnBC,OAAO;WADhBN,mBAAA,CAuBM;MArBHO,GAAG,EAAED,OAAO,CAACE,EAAE;MAChBX,KAAK,EAAC,cAAc;MACnBY,KAAK,EAhEdC,eAAA;QAAA,gBAgEkCJ,OAAO,CAACK,KAAK;QAAA,aAAeL,OAAO,CAACM;MAAO;MACpEC,OAAK,EAAAC,MAAA,IAAEV,MAAA,CAAAW,iBAAiB,CAACT,OAAO,CAACU,KAAK;QAEvChB,mBAAA,CAIM,OAJNiB,UAIM,GAHJC,YAAA,CAEUC,kBAAA;MAFAC,IAAI,EAAE,EAAE;MAAGT,KAAK,EAAEL,OAAO,CAACK;;MApE9CU,OAAA,EAAAC,QAAA,CAqEY,MAAgC,E,cAAhCC,YAAA,CAAgCC,wBArE5C,CAqE4BlB,OAAO,CAACmB,IAAI,I;MArExCC,CAAA;sDAyEQ1B,mBAAA,CAGM,OAHN2B,UAGM,GAFJ3B,mBAAA,CAA4B,YAAA4B,gBAAA,CAArBtB,OAAO,CAACuB,KAAK,kBACpB7B,mBAAA,CAAgC,WAAA4B,gBAAA,CAA1BtB,OAAO,CAACwB,WAAW,iB,GAG3B9B,mBAAA,CAIM,OAJN+B,UAIM,GAHJb,YAAA,CAEYc,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAExB,KAAK,EA/E1CC,eAAA;QAAAwB,eAAA,EA+E+D5B,OAAO,CAACK,KAAK;QAAAwB,WAAA,EAAe7B,OAAO,CAACK;MAAK;;MA/ExGU,OAAA,EAAAC,QAAA,CA+E4G,MAElG,KAAAc,MAAA,QAAAA,MAAA,OAjFVC,gBAAA,CA+E4G,QAElG,E;MAjFVX,CAAA;8EAAAY,UAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}