{"ast": null, "code": "import FeatureCards from './FeatureCards.vue';\nexport default {\n  name: 'HomePage',\n  components: {\n    FeatureCards\n  }\n};", "map": {"version": 3, "names": ["FeatureCards", "name", "components"], "sources": ["C:\\Users\\<USER>\\Desktop\\shishuo\\vue\\src\\components\\home\\home.vue"], "sourcesContent": ["<script>\r\nimport FeatureCards from './FeatureCards.vue'\r\n\r\nexport default {\r\n  name: 'HomePage',\r\n  components: {\r\n    FeatureCards\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"home-container\">\r\n    <FeatureCards />\r\n  </div>\r\n</template>\r\n\r\n<style scoped>\r\n.home-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n}\r\n</style>\r\n"], "mappings": "AACA,OAAOA,YAAW,MAAO,oBAAmB;AAE5C,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE;IACVF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}